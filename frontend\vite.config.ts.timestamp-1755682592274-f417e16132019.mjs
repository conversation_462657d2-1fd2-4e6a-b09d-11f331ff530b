// vite.config.ts
import { defineConfig, loadEnv } from "file:///E:/%E5%B7%A5%E5%85%B7%E8%BD%AF%E4%BB%B6/visualdebug/frontend/node_modules/vite/dist/node/index.js";
import { resolve as resolve2 } from "path";

// build/getEnv.ts
function wrapperEnv(envConf) {
  const ret = {};
  for (const envName of Object.keys(envConf)) {
    let realName = envConf[envName].replace(/\\n/g, "\n");
    realName = realName === "true" ? true : realName === "false" ? false : realName;
    if (envName === "VITE_PORT") realName = Number(realName);
    if (envName === "VITE_PROXY") {
      try {
        realName = JSON.parse(realName);
      } catch (error) {
      }
    }
    ret[envName] = realName;
  }
  return ret;
}

// build/proxy.ts
function createProxy(list = []) {
  const ret = {};
  for (const [prefix, target] of list) {
    const httpsRE = /^https:\/\//;
    const isHttps = httpsRE.test(target);
    ret[prefix] = {
      target,
      changeOrigin: true,
      ws: true,
      rewrite: (path) => path.replace(new RegExp(`^${prefix}`), ""),
      // https is require secure=false
      ...isHttps ? { secure: false } : {}
    };
  }
  return ret;
}

// build/plugins.ts
import { resolve } from "path";
import { VitePWA } from "file:///E:/%E5%B7%A5%E5%85%B7%E8%BD%AF%E4%BB%B6/visualdebug/frontend/node_modules/vite-plugin-pwa/dist/index.js";
import { visualizer } from "file:///E:/%E5%B7%A5%E5%85%B7%E8%BD%AF%E4%BB%B6/visualdebug/frontend/node_modules/rollup-plugin-visualizer/dist/plugin/index.js";
import { createSvgIconsPlugin } from "file:///E:/%E5%B7%A5%E5%85%B7%E8%BD%AF%E4%BB%B6/visualdebug/frontend/node_modules/vite-plugin-svg-icons/dist/index.mjs";
import { createHtmlPlugin } from "file:///E:/%E5%B7%A5%E5%85%B7%E8%BD%AF%E4%BB%B6/visualdebug/frontend/node_modules/vite-plugin-html/dist/index.mjs";
import vue from "file:///E:/%E5%B7%A5%E5%85%B7%E8%BD%AF%E4%BB%B6/visualdebug/frontend/node_modules/@vitejs/plugin-vue/dist/index.mjs";
import vueJsx from "file:///E:/%E5%B7%A5%E5%85%B7%E8%BD%AF%E4%BB%B6/visualdebug/frontend/node_modules/@vitejs/plugin-vue-jsx/dist/index.mjs";
import eslintPlugin from "file:///E:/%E5%B7%A5%E5%85%B7%E8%BD%AF%E4%BB%B6/visualdebug/frontend/node_modules/vite-plugin-eslint/dist/index.mjs";
import viteCompression from "file:///E:/%E5%B7%A5%E5%85%B7%E8%BD%AF%E4%BB%B6/visualdebug/frontend/node_modules/vite-plugin-compression/dist/index.mjs";
import vueSetupExtend from "file:///E:/%E5%B7%A5%E5%85%B7%E8%BD%AF%E4%BB%B6/visualdebug/frontend/node_modules/unplugin-vue-setup-extend-plus/dist/vite.js";
import UnoCSS from "file:///E:/%E5%B7%A5%E5%85%B7%E8%BD%AF%E4%BB%B6/visualdebug/frontend/node_modules/unocss/dist/vite.mjs";
import AutoImport from "file:///E:/%E5%B7%A5%E5%85%B7%E8%BD%AF%E4%BB%B6/visualdebug/frontend/node_modules/unplugin-auto-import/dist/vite.js";
import { ElementPlusResolver } from "file:///E:/%E5%B7%A5%E5%85%B7%E8%BD%AF%E4%BB%B6/visualdebug/frontend/node_modules/unplugin-vue-components/dist/resolvers.mjs";
import Components from "file:///E:/%E5%B7%A5%E5%85%B7%E8%BD%AF%E4%BB%B6/visualdebug/frontend/node_modules/unplugin-vue-components/dist/vite.mjs";
import Icons from "file:///E:/%E5%B7%A5%E5%85%B7%E8%BD%AF%E4%BB%B6/visualdebug/frontend/node_modules/unplugin-icons/dist/vite.js";
import IconsResolver from "file:///E:/%E5%B7%A5%E5%85%B7%E8%BD%AF%E4%BB%B6/visualdebug/frontend/node_modules/unplugin-icons/dist/resolver.js";
import NextDevTools from "file:///E:/%E5%B7%A5%E5%85%B7%E8%BD%AF%E4%BB%B6/visualdebug/frontend/node_modules/vite-plugin-vue-devtools/dist/vite.mjs";
import { codeInspectorPlugin } from "file:///E:/%E5%B7%A5%E5%85%B7%E8%BD%AF%E4%BB%B6/visualdebug/frontend/node_modules/code-inspector-plugin/dist/index.mjs";
var createVitePlugins = (viteEnv) => {
  const { VITE_GLOB_APP_TITLE, VITE_REPORT, VITE_DEVTOOLS, VITE_PWA, VITE_CODEINSPECTOR } = viteEnv;
  return [
    vue(),
    // devTools
    VITE_DEVTOOLS && NextDevTools({ launchEditor: "code" }),
    // vue 可以使用 jsx/tsx 语法
    vueJsx(),
    // esLint 报错信息显示在浏览器界面上
    eslintPlugin(),
    // name 可以写在 script 标签上
    vueSetupExtend({}),
    // 创建打包压缩配置
    createCompression(viteEnv),
    // 注入变量到 html 文件
    createHtmlPlugin({
      minify: true,
      inject: {
        data: { title: VITE_GLOB_APP_TITLE }
      }
    }),
    // 使用 svg 图标
    createSvgIconsPlugin({
      iconDirs: [resolve(process.cwd(), "src/assets/svg")],
      symbolId: "local-[dir]-[name]"
    }),
    // vitePWA
    VITE_PWA && createVitePwa(viteEnv),
    // 是否生成包预览，分析依赖包大小做优化处理
    VITE_REPORT && visualizer({ filename: "stats.html", gzipSize: true, brotliSize: true }),
    // 自动 IDE 并将光标定位到 DOM 对应的源代码位置。see: https://inspector.fe-dev.cn/guide/start.html
    VITE_CODEINSPECTOR && codeInspectorPlugin({
      bundler: "vite"
    }),
    // 自动导入组件
    AutoImport({
      imports: ["vue", "vue-router"],
      // Auto import functions from Element Plus, e.g. ElMessage, ElMessageBox... (without style)
      // 自动导入 Element Plus 相关函数，如：ElMessage, ElMessageBox... (不带样式，因为已在main.ts中导入完整样式)
      resolvers: [
        ElementPlusResolver({
          // 禁用样式自动导入，因为已在main.ts中导入完整样式
          importStyle: false
        }),
        // Auto import icon components
        // 自动导入图标组件
        IconsResolver({
          prefix: "Icon"
        })
      ],
      dts: "src/auto-import.d.ts"
      // 路径下自动生成文件夹存放全局指令
    }),
    Components({
      dirs: ["src/components"],
      // 配置需要默认导入的自定义组件文件夹，该文件夹下的所有组件都会自动 import
      resolvers: [
        // Auto register icon components
        // 自动注册图标组件
        IconsResolver({
          enabledCollections: ["ep"]
          // element-plus 图标库
        }),
        // Auto register Element Plus components
        // 自动导入 Element Plus 组件（不带样式，因为已在main.ts中导入完整样式）
        ElementPlusResolver({
          // 禁用样式自动导入，因为已在main.ts中导入完整样式
          importStyle: false,
          // 确保搜索表单组件能被正确解析
          resolveIcons: true
        })
      ],
      // 生成类型声明文件
      dts: true
    }),
    Icons({
      compiler: "vue3",
      autoInstall: true
    }),
    UnoCSS()
    // UnoCSS
  ];
};
var createCompression = (viteEnv) => {
  const { VITE_BUILD_COMPRESS = "none", VITE_BUILD_COMPRESS_DELETE_ORIGIN_FILE } = viteEnv;
  const compressList = VITE_BUILD_COMPRESS.split(",");
  const plugins = [];
  if (compressList.includes("gzip")) {
    plugins.push(
      viteCompression({
        ext: ".gz",
        algorithm: "gzip",
        deleteOriginFile: VITE_BUILD_COMPRESS_DELETE_ORIGIN_FILE
      })
    );
  }
  if (compressList.includes("brotli")) {
    plugins.push(
      viteCompression({
        ext: ".br",
        algorithm: "brotliCompress",
        deleteOriginFile: VITE_BUILD_COMPRESS_DELETE_ORIGIN_FILE
      })
    );
  }
  return plugins;
};
var createVitePwa = (viteEnv) => {
  const { VITE_GLOB_APP_TITLE } = viteEnv;
  return VitePWA({
    registerType: "autoUpdate",
    workbox: {
      // 添加此项配置，增加需要缓存的最大文件大小
      maximumFileSizeToCacheInBytes: 6 * 1024 * 1024
    },
    manifest: {
      name: VITE_GLOB_APP_TITLE,
      short_name: VITE_GLOB_APP_TITLE,
      theme_color: "#ffffff",
      icons: [
        {
          src: "/logo.png",
          sizes: "192x192",
          type: "image/png"
        },
        {
          src: "/logo.png",
          sizes: "512x512",
          type: "image/png"
        },
        {
          src: "/logo.png",
          sizes: "512x512",
          type: "image/png",
          purpose: "any maskable"
        }
      ]
    }
  });
};

// vite.config.ts
import { visualizer as visualizer2 } from "file:///E:/%E5%B7%A5%E5%85%B7%E8%BD%AF%E4%BB%B6/visualdebug/frontend/node_modules/rollup-plugin-visualizer/dist/plugin/index.js";

// package.json
var package_default = {
  name: "VisualDebug",
  private: true,
  type: "module",
  description: "\u53EF\u89C6\u5316\u5E73\u53F0\u5DE5\u7A0B\u8C03\u8BD5\u5DE5\u5177",
  license: "MIT",
  scripts: {
    dev: "vite --host --port 8080",
    "dev:force": "vite --host --port 8080 --force",
    build: "node ./node_modules/vite/bin/vite.js build",
    "build:dev": "vue-tsc && vite build --mode development",
    "build:test": "vue-tsc && vite build --mode test",
    "build:pro": "vue-tsc && vite build --mode production",
    "build:analyze": "vite build --mode production && npx vite-bundle-analyzer dist/stats.html",
    "type:check": "vue-tsc --noEmit --skipLibCheck",
    preview: "pnpm run build:dev && vite preview",
    "lint:eslint": "eslint --fix --ext .js,.ts,.vue ./src",
    "lint:prettier": 'prettier --write "src/**/*.{js,ts,json,tsx,css,less,scss,vue,html,md}"',
    "lint:stylelint": 'stylelint --cache --fix "**/*.{vue,less,postcss,css,scss}" --cache --cache-location node_modules/.cache/stylelint/',
    "lint:lint-staged": "lint-staged",
    "cache:clear": "node scripts/clear-cache.js",
    "cache:clear:dev": "node scripts/clear-cache.js --dev",
    "cache:clear:build": "node scripts/clear-cache.js --build",
    "cache:clear:all": "node scripts/clear-cache.js --all",
    "deps:update": "npm update && npm audit fix",
    "deps:check": "npm outdated",
    "perf:monitor": "node scripts/performance-monitor.js",
    "perf:cache": "node scripts/performance-monitor.js --cache",
    "perf:build": "node scripts/performance-monitor.js --build",
    prepare: "husky install",
    release: "standard-version",
    commit: "git add -A && czg && git push"
  },
  dependencies: {
    "@antv/g2plot": "^2.4.32",
    "@antv/x6": "^2.18.1",
    "@antv/x6-plugin-clipboard": "2.1.6",
    "@antv/x6-plugin-dnd": "2.1.1",
    "@antv/x6-plugin-export": "^2.1.6",
    "@antv/x6-plugin-history": "2.2.4",
    "@antv/x6-plugin-keyboard": "2.2.3",
    "@antv/x6-plugin-scroller": "2.0.10",
    "@antv/x6-plugin-selection": "2.2.2",
    "@antv/x6-plugin-snapline": "2.1.7",
    "@antv/x6-plugin-transform": "^2.1.8",
    "@element-plus/icons-vue": "^2.3.1",
    "@highlightjs/vue-plugin": "^2.1.0",
    "@iconify/vue": "^4.1.2",
    "@vueuse/core": "^11.0.3",
    axios: "^1.7.7",
    "crypto-js": "^4.1.1",
    dayjs: "^1.11.13",
    "decimal.js": "^10.5.0",
    "default-passive-events": "^2.0.0",
    echarts: "^5.5.1",
    "element-plus": "^2.5.6",
    entities: "^4.5.0",
    "highlight.js": "^11.10.0",
    "markdown-it": "^14.1.0",
    md5: "^2.3.0",
    mitt: "^3.0.1",
    nprogress: "^0.2.0",
    pinia: "^2.2.2",
    "pinia-plugin-persistedstate": "^3.2.1",
    "print-js": "^1.6.0",
    qs: "^6.13.0",
    sortablejs: "^1.15.3",
    "split.js": "^1.6.5",
    "sprintf-js": "^1.1.3",
    "v-contextmenu": "^3.2.0",
    vue: "^3.5.5",
    "vue-cropper": "^1.1.1",
    "vue-i18n": "^9.13.1",
    "vue-router": "^4.4.5"
  },
  devDependencies: {
    "@commitlint/cli": "^19.5.0",
    "@commitlint/config-conventional": "^19.5.0",
    "@iconify/json": "^2.2.247",
    "@types/markdown-it": "^14.1.2",
    "@types/md5": "^2.3.5",
    "@types/nprogress": "^0.2.3",
    "@types/qs": "^6.9.15",
    "@types/sm-crypto": "^0.3.4",
    "@types/sortablejs": "^1.15.8",
    "@types/uuid": "^10.0.0",
    "@typescript-eslint/eslint-plugin": "^7.14.1",
    "@typescript-eslint/parser": "^7.14.1",
    "@vitejs/plugin-vue": "^5.1.3",
    "@vitejs/plugin-vue-jsx": "^4.0.1",
    autoprefixer: "^10.4.20",
    "code-inspector-plugin": "^0.16.1",
    "cz-git": "^1.9.4",
    czg: "^1.9.4",
    eslint: "^8.57.0",
    "eslint-config-prettier": "^9.1.0",
    "eslint-plugin-prettier": "^5.1.3",
    "eslint-plugin-vue": "^9.26.0",
    "hotkeys-js": "3.13.7",
    husky: "^9.0.11",
    "lint-staged": "^15.2.10",
    "naive-ui": "^2.39.0",
    postcss: "^8.4.45",
    "postcss-html": "^1.7.0",
    prettier: "^3.3.3",
    "rollup-plugin-visualizer": "^5.14.0",
    sass: "1.74.1",
    "sm-crypto": "^0.3.13",
    "standard-version": "^9.5.0",
    stylelint: "^16.9.0",
    "stylelint-config-html": "^1.1.0",
    "stylelint-config-recess-order": "^5.1.0",
    "stylelint-config-recommended-scss": "^14.1.0",
    "stylelint-config-recommended-vue": "^1.5.0",
    "stylelint-config-standard": "^36.0.1",
    "stylelint-config-standard-scss": "^13.1.0",
    typescript: "~5.4.0",
    unocss: "^0.62.3",
    "unplugin-auto-import": "^0.18.3",
    "unplugin-icons": "^0.19.3",
    "unplugin-vue-components": "^0.25.2",
    "unplugin-vue-setup-extend-plus": "^1.0.1",
    uuid: "^8.3.2",
    vite: "^5.4.5",
    "vite-plugin-compression": "^0.5.1",
    "vite-plugin-eslint": "^1.8.1",
    "vite-plugin-html": "^3.2.2",
    "vite-plugin-pwa": "^0.20.5",
    "vite-plugin-svg-icons": "^2.0.1",
    "vite-plugin-vue-devtools": "^7.3.5",
    "vue-tsc": "^2.1.6"
  },
  overrides: {},
  engines: {
    node: ">=16.0.0"
  },
  browserslist: {
    production: [
      "> 1%",
      "not dead",
      "not op_mini all"
    ],
    development: [
      "last 1 chrome version",
      "last 1 firefox version",
      "last 1 safari version"
    ]
  },
  config: {
    commitizen: {
      path: "node_modules/cz-git"
    }
  }
};

// vite.config.ts
import dayjs from "file:///E:/%E5%B7%A5%E5%85%B7%E8%BD%AF%E4%BB%B6/visualdebug/frontend/node_modules/dayjs/dayjs.min.js";

// build/element-plus-config.ts
var elementPlusCoreIncludes = [
  // Element Plus 核心库
  "element-plus",
  "element-plus/es",
  "@element-plus/icons-vue"
];
var elementPlusStylePaths = [
  // 表单组件
  "element-plus/es/components/form/style/css",
  "element-plus/es/components/form-item/style/css",
  "element-plus/es/components/input/style/css",
  "element-plus/es/components/input-number/style/css",
  "element-plus/es/components/select/style/css",
  "element-plus/es/components/option/style/css",
  "element-plus/es/components/checkbox/style/css",
  "element-plus/es/components/radio/style/css",
  "element-plus/es/components/switch/style/css",
  "element-plus/es/components/slider/style/css",
  "element-plus/es/components/rate/style/css",
  "element-plus/es/components/color-picker/style/css",
  "element-plus/es/components/date-picker/style/css",
  "element-plus/es/components/time-picker/style/css",
  "element-plus/es/components/upload/style/css",
  // 数据展示组件
  "element-plus/es/components/table/style/css",
  "element-plus/es/components/table-column/style/css",
  "element-plus/es/components/pagination/style/css",
  "element-plus/es/components/tag/style/css",
  "element-plus/es/components/progress/style/css",
  "element-plus/es/components/tree/style/css",
  "element-plus/es/components/badge/style/css",
  "element-plus/es/components/card/style/css",
  "element-plus/es/components/collapse/style/css",
  "element-plus/es/components/timeline/style/css",
  "element-plus/es/components/divider/style/css",
  "element-plus/es/components/image/style/css",
  "element-plus/es/components/calendar/style/css",
  // 导航组件
  "element-plus/es/components/menu/style/css",
  "element-plus/es/components/tabs/style/css",
  "element-plus/es/components/breadcrumb/style/css",
  "element-plus/es/components/breadcrumb-item/style/css",
  "element-plus/es/components/page-header/style/css",
  "element-plus/es/components/steps/style/css",
  "element-plus/es/components/dropdown/style/css",
  "element-plus/es/components/dropdown-menu/style/css",
  "element-plus/es/components/dropdown-item/style/css",
  // 反馈组件
  "element-plus/es/components/alert/style/css",
  "element-plus/es/components/loading/style/css",
  "element-plus/es/components/message/style/css",
  "element-plus/es/components/message-box/style/css",
  "element-plus/es/components/notification/style/css",
  "element-plus/es/components/dialog/style/css",
  "element-plus/es/components/popover/style/css",
  "element-plus/es/components/popconfirm/style/css",
  "element-plus/es/components/tooltip/style/css",
  "element-plus/es/components/drawer/style/css",
  "element-plus/es/components/result/style/css",
  // 布局组件
  "element-plus/es/components/container/style/css",
  "element-plus/es/components/header/style/css",
  "element-plus/es/components/aside/style/css",
  "element-plus/es/components/main/style/css",
  "element-plus/es/components/footer/style/css",
  "element-plus/es/components/row/style/css",
  "element-plus/es/components/col/style/css",
  "element-plus/es/components/space/style/css",
  // 其他组件
  "element-plus/es/components/button/style/css",
  "element-plus/es/components/button-group/style/css",
  "element-plus/es/components/link/style/css",
  "element-plus/es/components/text/style/css",
  "element-plus/es/components/scrollbar/style/css",
  "element-plus/es/components/backtop/style/css",
  "element-plus/es/components/avatar/style/css",
  "element-plus/es/components/empty/style/css",
  "element-plus/es/components/descriptions/style/css",
  "element-plus/es/components/descriptions-item/style/css",
  "element-plus/es/components/skeleton/style/css",
  "element-plus/es/components/skeleton-item/style/css",
  "element-plus/es/components/affix/style/css",
  "element-plus/es/components/anchor/style/css",
  "element-plus/es/components/anchor-link/style/css"
];
function getElementPlusIncludes(useStyleIncludes = false) {
  if (useStyleIncludes) {
    return [...elementPlusCoreIncludes, ...elementPlusStylePaths];
  }
  return elementPlusCoreIncludes;
}
function isElementPlusModule(id) {
  return id.includes("element-plus") || id.includes("@element-plus");
}
function getElementPlusChunkName(id) {
  if (id.includes("@element-plus/icons-vue")) {
    return "element-icons";
  }
  if (id.includes("element-plus/es/components")) {
    return "element-components";
  }
  if (id.includes("element-plus")) {
    return "element-core";
  }
  return "element-vendor";
}

// vite.config.ts
var __vite_injected_original_dirname = "E:\\\u5DE5\u5177\u8F6F\u4EF6\\visualdebug\\frontend";
var { dependencies, devDependencies, name } = package_default;
var __APP_INFO__ = {
  pkg: { dependencies, devDependencies, name },
  lastBuildTime: dayjs().format("YYYY-MM-DD HH:mm:ss")
};
var vite_config_default = defineConfig(({ mode }) => {
  const root = process.cwd();
  const env = loadEnv(mode, root);
  const viteEnv = wrapperEnv(env);
  return {
    base: viteEnv.VITE_PUBLIC_PATH,
    root,
    resolve: {
      alias: {
        "@": resolve2(__vite_injected_original_dirname, "./src"),
        "vue-i18n": "vue-i18n/dist/vue-i18n.cjs.js",
        "async-validator": resolve2("node_modules/async-validator/dist-node/index.js"),
        mousetrap: "mousetrap/mousetrap.js"
      }
    },
    define: {
      __APP_INFO__: JSON.stringify(__APP_INFO__)
    },
    css: {
      preprocessorOptions: {
        scss: {
          api: "modern-compiler"
          // or "modern"
        }
      }
    },
    optimizeDeps: {
      include: [
        // 核心框架依赖 - 高优先级预构建
        "vue",
        "vue-router",
        "pinia",
        "pinia-plugin-persistedstate",
        // UI组件库 - 使用配置文件管理
        ...getElementPlusIncludes(false),
        // false表示不包含样式路径，因为已导入完整样式
        // 工具库 - 稳定依赖预构建
        "axios",
        "dayjs",
        "dayjs/locale/zh-cn",
        "lodash",
        "lodash-es",
        "@vueuse/core",
        "mitt",
        "nprogress",
        "qs",
        // 加密和工具
        "crypto-js",
        "md5",
        // 国际化
        "vue-i18n",
        // 数学计算
        "decimal.js",
        // 打印功能
        "print-js",
        // 拖拽排序
        "sortablejs",
        // 分割面板
        "split.js",
        // 字符串格式化
        "sprintf-js",
        // 键盘快捷键库
        "mousetrap"
      ],
      // 排除大型库和动态导入的依赖
      exclude: [
        // 图标库 - 按需加载
        "@iconify/json",
        "@iconify/vue",
        // 图表库 - 延迟加载
        "echarts",
        "@antv/g2plot",
        "@antv/x6",
        "@antv/x6-plugin-clipboard",
        "@antv/x6-plugin-dnd",
        "@antv/x6-plugin-export",
        "@antv/x6-plugin-history",
        "@antv/x6-plugin-keyboard",
        "@antv/x6-plugin-scroller",
        "@antv/x6-plugin-selection",
        "@antv/x6-plugin-snapline",
        "@antv/x6-plugin-transform",
        // 代码高亮 - 按需加载
        "highlight.js",
        "@highlightjs/vue-plugin",
        // Markdown处理 - 按需加载
        "markdown-it",
        // 图片裁剪 - 按需加载
        "vue-cropper",
        // 右键菜单 - 按需加载
        "v-contextmenu",
        // 实体编码 - 小型库
        "entities"
      ],
      // 开发环境强制重新预构建（生产环境设为false）
      force: process.env.NODE_ENV === "development" ? false : false,
      // 预构建入口
      entries: ["src/main.ts", "src/App.vue"]
    },
    server: {
      host: "0.0.0.0",
      port: viteEnv.VITE_PORT,
      open: viteEnv.VITE_OPEN,
      cors: true,
      // 开发服务器缓存配置
      fs: {
        // 允许访问工作区根目录之外的文件
        strict: false,
        // 缓存策略
        cachedChecks: true
      },
      // 预热常用文件，提升开发体验
      warmup: {
        clientFiles: ["src/main.ts", "src/App.vue", "src/layouts/index.vue", "src/routers/index.ts", "src/stores/index.ts"]
      },
      // Load proxy configuration from .env.development
      proxy: createProxy(viteEnv.VITE_PROXY)
    },
    plugins: [
      ...createVitePlugins(viteEnv),
      // Element Plus 配置已在 build/plugins.ts 中处理
      visualizer2({
        open: true,
        gzipSize: true,
        brotliSize: true,
        filename: "dist/stats.html"
      }),
      // 过滤 sourcemap 警告的插件
      {
        name: "suppress-sourcemap-warnings",
        buildStart() {
          const originalWarn = console.warn;
          console.warn = (...args) => {
            const message = args.join(" ");
            if (message.includes("Sourcemap for") && message.includes("points to missing source files")) {
              return;
            }
            if (message.includes("entities/lib/esm") && message.includes("points to missing source files")) {
              return;
            }
            originalWarn.apply(console, args);
          };
        },
        configureServer() {
          const originalWarn = console.warn;
          console.warn = (...args) => {
            const message = args.join(" ");
            if (message.includes("Sourcemap for") && message.includes("points to missing source files")) {
              return;
            }
            if (message.includes("entities/lib/esm") && message.includes("points to missing source files")) {
              return;
            }
            originalWarn.apply(console, args);
          };
        }
      }
    ],
    esbuild: {
      pure: viteEnv.VITE_DROP_CONSOLE ? ["console.log", "debugger"] : []
    },
    build: {
      outDir: "dist",
      minify: "esbuild",
      sourcemap: false,
      // 禁用 gzip 压缩大小报告，可略微减少打包时间
      reportCompressedSize: false,
      // 规定触发警告的 chunk 大小
      chunkSizeWarningLimit: 2e3,
      // 构建缓存配置
      emptyOutDir: true,
      // 静态资源处理
      assetsInlineLimit: 4096,
      // 小于4kb的资源内联为base64
      rollupOptions: {
        // 忽略有问题的 sourcemap 警告
        onwarn(warning, warn) {
          if (warning.code === "SOURCEMAP_ERROR") return;
          if (warning.message?.includes("entities/lib/esm") && warning.message?.includes("points to missing source files")) {
            return;
          }
          warn(warning);
        },
        // 缓存优化
        cache: true,
        // 外部依赖（如果需要CDN加载）
        external: [],
        output: {
          // 优化代码分割策略 - 基于缓存友好的文件名
          chunkFileNames: (chunkInfo) => {
            if (chunkInfo.name?.includes("vendor")) {
              return "assets/vendor/[name]-[hash].js";
            }
            if (chunkInfo.name?.includes("async")) {
              return "assets/async/[name]-[hash].js";
            }
            return "assets/chunks/[name]-[hash].js";
          },
          entryFileNames: "assets/entry/[name]-[hash].js",
          assetFileNames: (assetInfo) => {
            const extType = assetInfo.name?.split(".").pop() || "";
            if (["png", "jpg", "jpeg", "gif", "svg", "webp"].includes(extType)) {
              return "assets/images/[name]-[hash].[ext]";
            }
            if (["woff", "woff2", "ttf", "eot"].includes(extType)) {
              return "assets/fonts/[name]-[hash].[ext]";
            }
            if (["css"].includes(extType)) {
              return "assets/styles/[name]-[hash].[ext]";
            }
            return "assets/[ext]/[name]-[hash].[ext]";
          },
          // 手动分割代码块，优化缓存策略
          manualChunks: (id) => {
            if (id.includes("vue") && !id.includes("node_modules")) {
              return "vue-vendor";
            }
            if (id.includes("vue-router")) {
              return "vue-vendor";
            }
            if (id.includes("pinia")) {
              return "vue-vendor";
            }
            if (isElementPlusModule(id)) {
              return getElementPlusChunkName(id);
            }
            if (id.includes("@iconify")) {
              return "icons-vendor";
            }
            if (id.includes("axios") || id.includes("dayjs") || id.includes("lodash")) {
              return "utils-vendor";
            }
            if (id.includes("crypto-js") || id.includes("md5") || id.includes("qs")) {
              return "utils-vendor";
            }
            if (id.includes("echarts") || id.includes("@antv")) {
              return "charts-vendor";
            }
            if (id.includes("highlight.js") || id.includes("@highlightjs")) {
              return "highlight-vendor";
            }
            if (id.includes("vue-i18n")) {
              return "i18n-vendor";
            }
            if (id.includes("node_modules")) {
              return "vendor";
            }
            if (id.includes("src/views/")) {
              const match = id.match(/src\/views\/([^\/]+)/);
              if (match) {
                return `views-${match[1]}`;
              }
            }
            if (id.includes("src/components/")) {
              return "components";
            }
            if (id.includes("src/utils/") || id.includes("src/hooks/")) {
              return "utils";
            }
          }
        }
      }
    }
  };
});
export {
  vite_config_default as default
};
//# sourceMappingURL=data:application/json;base64,ewogICJ2ZXJzaW9uIjogMywKICAic291cmNlcyI6IFsidml0ZS5jb25maWcudHMiLCAiYnVpbGQvZ2V0RW52LnRzIiwgImJ1aWxkL3Byb3h5LnRzIiwgImJ1aWxkL3BsdWdpbnMudHMiLCAicGFja2FnZS5qc29uIiwgImJ1aWxkL2VsZW1lbnQtcGx1cy1jb25maWcudHMiXSwKICAic291cmNlc0NvbnRlbnQiOiBbImNvbnN0IF9fdml0ZV9pbmplY3RlZF9vcmlnaW5hbF9kaXJuYW1lID0gXCJFOlxcXFxcdTVERTVcdTUxNzdcdThGNkZcdTRFRjZcXFxcdmlzdWFsZGVidWdcXFxcZnJvbnRlbmRcIjtjb25zdCBfX3ZpdGVfaW5qZWN0ZWRfb3JpZ2luYWxfZmlsZW5hbWUgPSBcIkU6XFxcXFx1NURFNVx1NTE3N1x1OEY2Rlx1NEVGNlxcXFx2aXN1YWxkZWJ1Z1xcXFxmcm9udGVuZFxcXFx2aXRlLmNvbmZpZy50c1wiO2NvbnN0IF9fdml0ZV9pbmplY3RlZF9vcmlnaW5hbF9pbXBvcnRfbWV0YV91cmwgPSBcImZpbGU6Ly8vRTovJUU1JUI3JUE1JUU1JTg1JUI3JUU4JUJEJUFGJUU0JUJCJUI2L3Zpc3VhbGRlYnVnL2Zyb250ZW5kL3ZpdGUuY29uZmlnLnRzXCI7aW1wb3J0IHsgZGVmaW5lQ29uZmlnLCBsb2FkRW52LCBDb25maWdFbnYsIFVzZXJDb25maWcgfSBmcm9tIFwidml0ZVwiO1xyXG5pbXBvcnQgeyByZXNvbHZlIH0gZnJvbSBcInBhdGhcIjtcclxuaW1wb3J0IHsgd3JhcHBlckVudiB9IGZyb20gXCIuL2J1aWxkL2dldEVudlwiO1xyXG5pbXBvcnQgeyBjcmVhdGVQcm94eSB9IGZyb20gXCIuL2J1aWxkL3Byb3h5XCI7XHJcbmltcG9ydCB7IGNyZWF0ZVZpdGVQbHVnaW5zIH0gZnJvbSBcIi4vYnVpbGQvcGx1Z2luc1wiO1xyXG5pbXBvcnQgeyB2aXN1YWxpemVyIH0gZnJvbSBcInJvbGx1cC1wbHVnaW4tdmlzdWFsaXplclwiO1xyXG5pbXBvcnQgcGtnIGZyb20gXCIuL3BhY2thZ2UuanNvblwiO1xyXG5pbXBvcnQgZGF5anMgZnJvbSBcImRheWpzXCI7XHJcbmltcG9ydCB7IGdldEVsZW1lbnRQbHVzSW5jbHVkZXMsIGlzRWxlbWVudFBsdXNNb2R1bGUsIGdldEVsZW1lbnRQbHVzQ2h1bmtOYW1lIH0gZnJvbSBcIi4vYnVpbGQvZWxlbWVudC1wbHVzLWNvbmZpZ1wiO1xyXG5cclxuY29uc3QgeyBkZXBlbmRlbmNpZXMsIGRldkRlcGVuZGVuY2llcywgbmFtZSB9ID0gcGtnO1xyXG5jb25zdCBfX0FQUF9JTkZPX18gPSB7XHJcbiAgcGtnOiB7IGRlcGVuZGVuY2llcywgZGV2RGVwZW5kZW5jaWVzLCBuYW1lIH0sXHJcbiAgbGFzdEJ1aWxkVGltZTogZGF5anMoKS5mb3JtYXQoXCJZWVlZLU1NLUREIEhIOm1tOnNzXCIpXHJcbn07XHJcblxyXG4vLyBAc2VlOiBodHRwczovL3ZpdGVqcy5kZXYvY29uZmlnL1xyXG5leHBvcnQgZGVmYXVsdCBkZWZpbmVDb25maWcoKHsgbW9kZSB9OiBDb25maWdFbnYpOiBVc2VyQ29uZmlnID0+IHtcclxuICBjb25zdCByb290ID0gcHJvY2Vzcy5jd2QoKTtcclxuICBjb25zdCBlbnYgPSBsb2FkRW52KG1vZGUsIHJvb3QpO1xyXG4gIGNvbnN0IHZpdGVFbnYgPSB3cmFwcGVyRW52KGVudik7XHJcblxyXG4gIHJldHVybiB7XHJcbiAgICBiYXNlOiB2aXRlRW52LlZJVEVfUFVCTElDX1BBVEgsXHJcbiAgICByb290LFxyXG4gICAgcmVzb2x2ZToge1xyXG4gICAgICBhbGlhczoge1xyXG4gICAgICAgIFwiQFwiOiByZXNvbHZlKF9fZGlybmFtZSwgXCIuL3NyY1wiKSxcclxuICAgICAgICBcInZ1ZS1pMThuXCI6IFwidnVlLWkxOG4vZGlzdC92dWUtaTE4bi5janMuanNcIixcclxuICAgICAgICBcImFzeW5jLXZhbGlkYXRvclwiOiByZXNvbHZlKFwibm9kZV9tb2R1bGVzL2FzeW5jLXZhbGlkYXRvci9kaXN0LW5vZGUvaW5kZXguanNcIiksXHJcbiAgICAgICAgbW91c2V0cmFwOiBcIm1vdXNldHJhcC9tb3VzZXRyYXAuanNcIlxyXG4gICAgICB9XHJcbiAgICB9LFxyXG4gICAgZGVmaW5lOiB7XHJcbiAgICAgIF9fQVBQX0lORk9fXzogSlNPTi5zdHJpbmdpZnkoX19BUFBfSU5GT19fKVxyXG4gICAgfSxcclxuICAgIGNzczoge1xyXG4gICAgICBwcmVwcm9jZXNzb3JPcHRpb25zOiB7XHJcbiAgICAgICAgc2Nzczoge1xyXG4gICAgICAgICAgYXBpOiBcIm1vZGVybi1jb21waWxlclwiIC8vIG9yIFwibW9kZXJuXCJcclxuICAgICAgICB9XHJcbiAgICAgIH1cclxuICAgIH0sXHJcbiAgICBvcHRpbWl6ZURlcHM6IHtcclxuICAgICAgaW5jbHVkZTogW1xyXG4gICAgICAgIC8vIFx1NjgzOFx1NUZDM1x1Njg0Nlx1NjdCNlx1NEY5RFx1OEQ1NiAtIFx1OUFEOFx1NEYxOFx1NTE0OFx1N0VBN1x1OTg4NFx1Njc4NFx1NUVGQVxyXG4gICAgICAgIFwidnVlXCIsXHJcbiAgICAgICAgXCJ2dWUtcm91dGVyXCIsXHJcbiAgICAgICAgXCJwaW5pYVwiLFxyXG4gICAgICAgIFwicGluaWEtcGx1Z2luLXBlcnNpc3RlZHN0YXRlXCIsXHJcblxyXG4gICAgICAgIC8vIFVJXHU3RUM0XHU0RUY2XHU1RTkzIC0gXHU0RjdGXHU3NTI4XHU5MTREXHU3RjZFXHU2NTg3XHU0RUY2XHU3QkExXHU3NDA2XHJcbiAgICAgICAgLi4uZ2V0RWxlbWVudFBsdXNJbmNsdWRlcyhmYWxzZSksIC8vIGZhbHNlXHU4ODY4XHU3OTNBXHU0RTBEXHU1MzA1XHU1NDJCXHU2ODM3XHU1RjBGXHU4REVGXHU1Rjg0XHVGRjBDXHU1NkUwXHU0RTNBXHU1REYyXHU1QkZDXHU1MTY1XHU1QjhDXHU2NTc0XHU2ODM3XHU1RjBGXHJcblxyXG4gICAgICAgIC8vIFx1NURFNVx1NTE3N1x1NUU5MyAtIFx1N0EzM1x1NUI5QVx1NEY5RFx1OEQ1Nlx1OTg4NFx1Njc4NFx1NUVGQVxyXG4gICAgICAgIFwiYXhpb3NcIixcclxuICAgICAgICBcImRheWpzXCIsXHJcbiAgICAgICAgXCJkYXlqcy9sb2NhbGUvemgtY25cIixcclxuICAgICAgICBcImxvZGFzaFwiLFxyXG4gICAgICAgIFwibG9kYXNoLWVzXCIsXHJcbiAgICAgICAgXCJAdnVldXNlL2NvcmVcIixcclxuICAgICAgICBcIm1pdHRcIixcclxuICAgICAgICBcIm5wcm9ncmVzc1wiLFxyXG4gICAgICAgIFwicXNcIixcclxuXHJcbiAgICAgICAgLy8gXHU1MkEwXHU1QkM2XHU1NDhDXHU1REU1XHU1MTc3XHJcbiAgICAgICAgXCJjcnlwdG8tanNcIixcclxuICAgICAgICBcIm1kNVwiLFxyXG5cclxuICAgICAgICAvLyBcdTU2RkRcdTk2NDVcdTUzMTZcclxuICAgICAgICBcInZ1ZS1pMThuXCIsXHJcblxyXG4gICAgICAgIC8vIFx1NjU3MFx1NUI2Nlx1OEJBMVx1N0I5N1xyXG4gICAgICAgIFwiZGVjaW1hbC5qc1wiLFxyXG5cclxuICAgICAgICAvLyBcdTYyNTNcdTUzNzBcdTUyOUZcdTgwRkRcclxuICAgICAgICBcInByaW50LWpzXCIsXHJcblxyXG4gICAgICAgIC8vIFx1NjJENlx1NjJGRFx1NjM5Mlx1NUU4RlxyXG4gICAgICAgIFwic29ydGFibGVqc1wiLFxyXG5cclxuICAgICAgICAvLyBcdTUyMDZcdTUyNzJcdTk3NjJcdTY3N0ZcclxuICAgICAgICBcInNwbGl0LmpzXCIsXHJcblxyXG4gICAgICAgIC8vIFx1NUI1N1x1N0IyNlx1NEUzMlx1NjgzQ1x1NUYwRlx1NTMxNlxyXG4gICAgICAgIFwic3ByaW50Zi1qc1wiLFxyXG5cclxuICAgICAgICAvLyBcdTk1MkVcdTc2RDhcdTVGRUJcdTYzNzdcdTk1MkVcdTVFOTNcclxuICAgICAgICBcIm1vdXNldHJhcFwiXHJcbiAgICAgIF0sXHJcblxyXG4gICAgICAvLyBcdTYzOTJcdTk2NjRcdTU5MjdcdTU3OEJcdTVFOTNcdTU0OENcdTUyQThcdTYwMDFcdTVCRkNcdTUxNjVcdTc2ODRcdTRGOURcdThENTZcclxuICAgICAgZXhjbHVkZTogW1xyXG4gICAgICAgIC8vIFx1NTZGRVx1NjgwN1x1NUU5MyAtIFx1NjMwOVx1OTcwMFx1NTJBMFx1OEY3RFxyXG4gICAgICAgIFwiQGljb25pZnkvanNvblwiLFxyXG4gICAgICAgIFwiQGljb25pZnkvdnVlXCIsXHJcblxyXG4gICAgICAgIC8vIFx1NTZGRVx1ODg2OFx1NUU5MyAtIFx1NUVGNlx1OEZERlx1NTJBMFx1OEY3RFxyXG4gICAgICAgIFwiZWNoYXJ0c1wiLFxyXG4gICAgICAgIFwiQGFudHYvZzJwbG90XCIsXHJcbiAgICAgICAgXCJAYW50di94NlwiLFxyXG4gICAgICAgIFwiQGFudHYveDYtcGx1Z2luLWNsaXBib2FyZFwiLFxyXG4gICAgICAgIFwiQGFudHYveDYtcGx1Z2luLWRuZFwiLFxyXG4gICAgICAgIFwiQGFudHYveDYtcGx1Z2luLWV4cG9ydFwiLFxyXG4gICAgICAgIFwiQGFudHYveDYtcGx1Z2luLWhpc3RvcnlcIixcclxuICAgICAgICBcIkBhbnR2L3g2LXBsdWdpbi1rZXlib2FyZFwiLFxyXG4gICAgICAgIFwiQGFudHYveDYtcGx1Z2luLXNjcm9sbGVyXCIsXHJcbiAgICAgICAgXCJAYW50di94Ni1wbHVnaW4tc2VsZWN0aW9uXCIsXHJcbiAgICAgICAgXCJAYW50di94Ni1wbHVnaW4tc25hcGxpbmVcIixcclxuICAgICAgICBcIkBhbnR2L3g2LXBsdWdpbi10cmFuc2Zvcm1cIixcclxuXHJcbiAgICAgICAgLy8gXHU0RUUzXHU3ODAxXHU5QUQ4XHU0RUFFIC0gXHU2MzA5XHU5NzAwXHU1MkEwXHU4RjdEXHJcbiAgICAgICAgXCJoaWdobGlnaHQuanNcIixcclxuICAgICAgICBcIkBoaWdobGlnaHRqcy92dWUtcGx1Z2luXCIsXHJcblxyXG4gICAgICAgIC8vIE1hcmtkb3duXHU1OTA0XHU3NDA2IC0gXHU2MzA5XHU5NzAwXHU1MkEwXHU4RjdEXHJcbiAgICAgICAgXCJtYXJrZG93bi1pdFwiLFxyXG5cclxuICAgICAgICAvLyBcdTU2RkVcdTcyNDdcdTg4QzFcdTUyNkEgLSBcdTYzMDlcdTk3MDBcdTUyQTBcdThGN0RcclxuICAgICAgICBcInZ1ZS1jcm9wcGVyXCIsXHJcblxyXG4gICAgICAgIC8vIFx1NTNGM1x1OTUyRVx1ODNEQ1x1NTM1NSAtIFx1NjMwOVx1OTcwMFx1NTJBMFx1OEY3RFxyXG4gICAgICAgIFwidi1jb250ZXh0bWVudVwiLFxyXG5cclxuICAgICAgICAvLyBcdTVCOUVcdTRGNTNcdTdGMTZcdTc4MDEgLSBcdTVDMEZcdTU3OEJcdTVFOTNcclxuICAgICAgICBcImVudGl0aWVzXCJcclxuICAgICAgXSxcclxuXHJcbiAgICAgIC8vIFx1NUYwMFx1NTNEMVx1NzNBRlx1NTg4M1x1NUYzQVx1NTIzNlx1OTFDRFx1NjVCMFx1OTg4NFx1Njc4NFx1NUVGQVx1RkYwOFx1NzUxRlx1NEVBN1x1NzNBRlx1NTg4M1x1OEJCRVx1NEUzQWZhbHNlXHVGRjA5XHJcbiAgICAgIGZvcmNlOiBwcm9jZXNzLmVudi5OT0RFX0VOViA9PT0gXCJkZXZlbG9wbWVudFwiID8gZmFsc2UgOiBmYWxzZSxcclxuXHJcbiAgICAgIC8vIFx1OTg4NFx1Njc4NFx1NUVGQVx1NTE2NVx1NTNFM1xyXG4gICAgICBlbnRyaWVzOiBbXCJzcmMvbWFpbi50c1wiLCBcInNyYy9BcHAudnVlXCJdXHJcbiAgICB9LFxyXG4gICAgc2VydmVyOiB7XHJcbiAgICAgIGhvc3Q6IFwiMC4wLjAuMFwiLFxyXG4gICAgICBwb3J0OiB2aXRlRW52LlZJVEVfUE9SVCxcclxuICAgICAgb3Blbjogdml0ZUVudi5WSVRFX09QRU4sXHJcbiAgICAgIGNvcnM6IHRydWUsXHJcblxyXG4gICAgICAvLyBcdTVGMDBcdTUzRDFcdTY3MERcdTUyQTFcdTU2NjhcdTdGMTNcdTVCNThcdTkxNERcdTdGNkVcclxuICAgICAgZnM6IHtcclxuICAgICAgICAvLyBcdTUxNDFcdThCQjhcdThCQkZcdTk1RUVcdTVERTVcdTRGNUNcdTUzM0FcdTY4MzlcdTc2RUVcdTVGNTVcdTRFNEJcdTU5MTZcdTc2ODRcdTY1ODdcdTRFRjZcclxuICAgICAgICBzdHJpY3Q6IGZhbHNlLFxyXG4gICAgICAgIC8vIFx1N0YxM1x1NUI1OFx1N0I1Nlx1NzU2NVxyXG4gICAgICAgIGNhY2hlZENoZWNrczogdHJ1ZVxyXG4gICAgICB9LFxyXG5cclxuICAgICAgLy8gXHU5ODg0XHU3MEVEXHU1RTM4XHU3NTI4XHU2NTg3XHU0RUY2XHVGRjBDXHU2M0QwXHU1MzQ3XHU1RjAwXHU1M0QxXHU0RjUzXHU5QThDXHJcbiAgICAgIHdhcm11cDoge1xyXG4gICAgICAgIGNsaWVudEZpbGVzOiBbXCJzcmMvbWFpbi50c1wiLCBcInNyYy9BcHAudnVlXCIsIFwic3JjL2xheW91dHMvaW5kZXgudnVlXCIsIFwic3JjL3JvdXRlcnMvaW5kZXgudHNcIiwgXCJzcmMvc3RvcmVzL2luZGV4LnRzXCJdXHJcbiAgICAgIH0sXHJcblxyXG4gICAgICAvLyBMb2FkIHByb3h5IGNvbmZpZ3VyYXRpb24gZnJvbSAuZW52LmRldmVsb3BtZW50XHJcbiAgICAgIHByb3h5OiBjcmVhdGVQcm94eSh2aXRlRW52LlZJVEVfUFJPWFkpXHJcbiAgICB9LFxyXG4gICAgcGx1Z2luczogW1xyXG4gICAgICAuLi5jcmVhdGVWaXRlUGx1Z2lucyh2aXRlRW52KSxcclxuICAgICAgLy8gRWxlbWVudCBQbHVzIFx1OTE0RFx1N0Y2RVx1NURGMlx1NTcyOCBidWlsZC9wbHVnaW5zLnRzIFx1NEUyRFx1NTkwNFx1NzQwNlxyXG4gICAgICB2aXN1YWxpemVyKHtcclxuICAgICAgICBvcGVuOiB0cnVlLFxyXG4gICAgICAgIGd6aXBTaXplOiB0cnVlLFxyXG4gICAgICAgIGJyb3RsaVNpemU6IHRydWUsXHJcbiAgICAgICAgZmlsZW5hbWU6IFwiZGlzdC9zdGF0cy5odG1sXCJcclxuICAgICAgfSksXHJcbiAgICAgIC8vIFx1OEZDN1x1NkVFNCBzb3VyY2VtYXAgXHU4QjY2XHU1NDRBXHU3Njg0XHU2M0QyXHU0RUY2XHJcbiAgICAgIHtcclxuICAgICAgICBuYW1lOiBcInN1cHByZXNzLXNvdXJjZW1hcC13YXJuaW5nc1wiLFxyXG4gICAgICAgIGJ1aWxkU3RhcnQoKSB7XHJcbiAgICAgICAgICAvLyBcdTU3MjhcdTY3ODRcdTVFRkFcdTVGMDBcdTU5Q0JcdTY1RjZcdTkxQ0RcdTUxOTkgY29uc29sZS53YXJuXHJcbiAgICAgICAgICBjb25zdCBvcmlnaW5hbFdhcm4gPSBjb25zb2xlLndhcm47XHJcbiAgICAgICAgICBjb25zb2xlLndhcm4gPSAoLi4uYXJnczogYW55W10pID0+IHtcclxuICAgICAgICAgICAgY29uc3QgbWVzc2FnZSA9IGFyZ3Muam9pbihcIiBcIik7XHJcbiAgICAgICAgICAgIC8vIFx1OEZDN1x1NkVFNFx1NjM4OSBzb3VyY2VtYXAgXHU3NkY4XHU1MTczXHU3Njg0XHU4QjY2XHU1NDRBXHJcbiAgICAgICAgICAgIGlmIChtZXNzYWdlLmluY2x1ZGVzKFwiU291cmNlbWFwIGZvclwiKSAmJiBtZXNzYWdlLmluY2x1ZGVzKFwicG9pbnRzIHRvIG1pc3Npbmcgc291cmNlIGZpbGVzXCIpKSB7XHJcbiAgICAgICAgICAgICAgcmV0dXJuO1xyXG4gICAgICAgICAgICB9XHJcbiAgICAgICAgICAgIC8vIFx1OEZDN1x1NkVFNFx1NjM4OSBlbnRpdGllcyBcdTc2RjhcdTUxNzNcdTc2ODRcdThCNjZcdTU0NEFcclxuICAgICAgICAgICAgaWYgKG1lc3NhZ2UuaW5jbHVkZXMoXCJlbnRpdGllcy9saWIvZXNtXCIpICYmIG1lc3NhZ2UuaW5jbHVkZXMoXCJwb2ludHMgdG8gbWlzc2luZyBzb3VyY2UgZmlsZXNcIikpIHtcclxuICAgICAgICAgICAgICByZXR1cm47XHJcbiAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgb3JpZ2luYWxXYXJuLmFwcGx5KGNvbnNvbGUsIGFyZ3MpO1xyXG4gICAgICAgICAgfTtcclxuICAgICAgICB9LFxyXG4gICAgICAgIGNvbmZpZ3VyZVNlcnZlcigpIHtcclxuICAgICAgICAgIC8vIFx1NTcyOFx1NUYwMFx1NTNEMVx1NjcwRFx1NTJBMVx1NTY2OFx1NTQyRlx1NTJBOFx1NjVGNlx1NEU1Rlx1OTFDRFx1NTE5OVxyXG4gICAgICAgICAgY29uc3Qgb3JpZ2luYWxXYXJuID0gY29uc29sZS53YXJuO1xyXG4gICAgICAgICAgY29uc29sZS53YXJuID0gKC4uLmFyZ3M6IGFueVtdKSA9PiB7XHJcbiAgICAgICAgICAgIGNvbnN0IG1lc3NhZ2UgPSBhcmdzLmpvaW4oXCIgXCIpO1xyXG4gICAgICAgICAgICBpZiAobWVzc2FnZS5pbmNsdWRlcyhcIlNvdXJjZW1hcCBmb3JcIikgJiYgbWVzc2FnZS5pbmNsdWRlcyhcInBvaW50cyB0byBtaXNzaW5nIHNvdXJjZSBmaWxlc1wiKSkge1xyXG4gICAgICAgICAgICAgIHJldHVybjtcclxuICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICBpZiAobWVzc2FnZS5pbmNsdWRlcyhcImVudGl0aWVzL2xpYi9lc21cIikgJiYgbWVzc2FnZS5pbmNsdWRlcyhcInBvaW50cyB0byBtaXNzaW5nIHNvdXJjZSBmaWxlc1wiKSkge1xyXG4gICAgICAgICAgICAgIHJldHVybjtcclxuICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICBvcmlnaW5hbFdhcm4uYXBwbHkoY29uc29sZSwgYXJncyk7XHJcbiAgICAgICAgICB9O1xyXG4gICAgICAgIH1cclxuICAgICAgfVxyXG4gICAgXSxcclxuICAgIGVzYnVpbGQ6IHtcclxuICAgICAgcHVyZTogdml0ZUVudi5WSVRFX0RST1BfQ09OU09MRSA/IFtcImNvbnNvbGUubG9nXCIsIFwiZGVidWdnZXJcIl0gOiBbXVxyXG4gICAgfSxcclxuICAgIGJ1aWxkOiB7XHJcbiAgICAgIG91dERpcjogXCJkaXN0XCIsXHJcbiAgICAgIG1pbmlmeTogXCJlc2J1aWxkXCIsXHJcbiAgICAgIHNvdXJjZW1hcDogZmFsc2UsXHJcblxyXG4gICAgICAvLyBcdTc5ODFcdTc1MjggZ3ppcCBcdTUzOEJcdTdGMjlcdTU5MjdcdTVDMEZcdTYyQTVcdTU0NEFcdUZGMENcdTUzRUZcdTc1NjVcdTVGQUVcdTUxQ0ZcdTVDMTFcdTYyNTNcdTUzMDVcdTY1RjZcdTk1RjRcclxuICAgICAgcmVwb3J0Q29tcHJlc3NlZFNpemU6IGZhbHNlLFxyXG5cclxuICAgICAgLy8gXHU4OUM0XHU1QjlBXHU4OUU2XHU1M0QxXHU4QjY2XHU1NDRBXHU3Njg0IGNodW5rIFx1NTkyN1x1NUMwRlxyXG4gICAgICBjaHVua1NpemVXYXJuaW5nTGltaXQ6IDIwMDAsXHJcblxyXG4gICAgICAvLyBcdTY3ODRcdTVFRkFcdTdGMTNcdTVCNThcdTkxNERcdTdGNkVcclxuICAgICAgZW1wdHlPdXREaXI6IHRydWUsXHJcblxyXG4gICAgICAvLyBcdTk3NTlcdTYwMDFcdThENDRcdTZFOTBcdTU5MDRcdTc0MDZcclxuICAgICAgYXNzZXRzSW5saW5lTGltaXQ6IDQwOTYsIC8vIFx1NUMwRlx1NEU4RTRrYlx1NzY4NFx1OEQ0NFx1NkU5MFx1NTE4NVx1ODA1NFx1NEUzQWJhc2U2NFxyXG5cclxuICAgICAgcm9sbHVwT3B0aW9uczoge1xyXG4gICAgICAgIC8vIFx1NUZGRFx1NzU2NVx1NjcwOVx1OTVFRVx1OTg5OFx1NzY4NCBzb3VyY2VtYXAgXHU4QjY2XHU1NDRBXHJcbiAgICAgICAgb253YXJuKHdhcm5pbmcsIHdhcm4pIHtcclxuICAgICAgICAgIC8vIFx1NUZGRFx1NzU2NSBzb3VyY2VtYXAgXHU3NkY4XHU1MTczXHU4QjY2XHU1NDRBXHJcbiAgICAgICAgICBpZiAod2FybmluZy5jb2RlID09PSBcIlNPVVJDRU1BUF9FUlJPUlwiKSByZXR1cm47XHJcbiAgICAgICAgICAvLyBcdTVGRkRcdTc1NjUgbWFya2Rvd24taXQgZW50aXRpZXMgXHU3Njg0IHNvdXJjZW1hcCBcdThCNjZcdTU0NEFcclxuICAgICAgICAgIGlmICh3YXJuaW5nLm1lc3NhZ2U/LmluY2x1ZGVzKFwiZW50aXRpZXMvbGliL2VzbVwiKSAmJiB3YXJuaW5nLm1lc3NhZ2U/LmluY2x1ZGVzKFwicG9pbnRzIHRvIG1pc3Npbmcgc291cmNlIGZpbGVzXCIpKSB7XHJcbiAgICAgICAgICAgIHJldHVybjtcclxuICAgICAgICAgIH1cclxuICAgICAgICAgIHdhcm4od2FybmluZyk7XHJcbiAgICAgICAgfSxcclxuICAgICAgICAvLyBcdTdGMTNcdTVCNThcdTRGMThcdTUzMTZcclxuICAgICAgICBjYWNoZTogdHJ1ZSxcclxuXHJcbiAgICAgICAgLy8gXHU1OTE2XHU5MEU4XHU0RjlEXHU4RDU2XHVGRjA4XHU1OTgyXHU2NzlDXHU5NzAwXHU4OTgxQ0ROXHU1MkEwXHU4RjdEXHVGRjA5XHJcbiAgICAgICAgZXh0ZXJuYWw6IFtdLFxyXG5cclxuICAgICAgICBvdXRwdXQ6IHtcclxuICAgICAgICAgIC8vIFx1NEYxOFx1NTMxNlx1NEVFM1x1NzgwMVx1NTIwNlx1NTI3Mlx1N0I1Nlx1NzU2NSAtIFx1NTdGQVx1NEU4RVx1N0YxM1x1NUI1OFx1NTNDQlx1NTk3RFx1NzY4NFx1NjU4N1x1NEVGNlx1NTQwRFxyXG4gICAgICAgICAgY2h1bmtGaWxlTmFtZXM6IGNodW5rSW5mbyA9PiB7XHJcbiAgICAgICAgICAgIC8vIFx1NEUzQVx1NEUwRFx1NTQwQ1x1N0M3Qlx1NTc4Qlx1NzY4NGNodW5rXHU0RjdGXHU3NTI4XHU0RTBEXHU1NDBDXHU3Njg0XHU1NDdEXHU1NDBEXHU3QjU2XHU3NTY1XHJcbiAgICAgICAgICAgIGlmIChjaHVua0luZm8ubmFtZT8uaW5jbHVkZXMoXCJ2ZW5kb3JcIikpIHtcclxuICAgICAgICAgICAgICByZXR1cm4gXCJhc3NldHMvdmVuZG9yL1tuYW1lXS1baGFzaF0uanNcIjtcclxuICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICBpZiAoY2h1bmtJbmZvLm5hbWU/LmluY2x1ZGVzKFwiYXN5bmNcIikpIHtcclxuICAgICAgICAgICAgICByZXR1cm4gXCJhc3NldHMvYXN5bmMvW25hbWVdLVtoYXNoXS5qc1wiO1xyXG4gICAgICAgICAgICB9XHJcbiAgICAgICAgICAgIHJldHVybiBcImFzc2V0cy9jaHVua3MvW25hbWVdLVtoYXNoXS5qc1wiO1xyXG4gICAgICAgICAgfSxcclxuICAgICAgICAgIGVudHJ5RmlsZU5hbWVzOiBcImFzc2V0cy9lbnRyeS9bbmFtZV0tW2hhc2hdLmpzXCIsXHJcbiAgICAgICAgICBhc3NldEZpbGVOYW1lczogYXNzZXRJbmZvID0+IHtcclxuICAgICAgICAgICAgLy8gXHU2ODM5XHU2MzZFXHU2NTg3XHU0RUY2XHU3QzdCXHU1NzhCXHU1MjA2XHU3QzdCXHU1QjU4XHU2NTNFXHJcbiAgICAgICAgICAgIGNvbnN0IGV4dFR5cGUgPSBhc3NldEluZm8ubmFtZT8uc3BsaXQoXCIuXCIpLnBvcCgpIHx8IFwiXCI7XHJcbiAgICAgICAgICAgIGlmIChbXCJwbmdcIiwgXCJqcGdcIiwgXCJqcGVnXCIsIFwiZ2lmXCIsIFwic3ZnXCIsIFwid2VicFwiXS5pbmNsdWRlcyhleHRUeXBlKSkge1xyXG4gICAgICAgICAgICAgIHJldHVybiBcImFzc2V0cy9pbWFnZXMvW25hbWVdLVtoYXNoXS5bZXh0XVwiO1xyXG4gICAgICAgICAgICB9XHJcbiAgICAgICAgICAgIGlmIChbXCJ3b2ZmXCIsIFwid29mZjJcIiwgXCJ0dGZcIiwgXCJlb3RcIl0uaW5jbHVkZXMoZXh0VHlwZSkpIHtcclxuICAgICAgICAgICAgICByZXR1cm4gXCJhc3NldHMvZm9udHMvW25hbWVdLVtoYXNoXS5bZXh0XVwiO1xyXG4gICAgICAgICAgICB9XHJcbiAgICAgICAgICAgIGlmIChbXCJjc3NcIl0uaW5jbHVkZXMoZXh0VHlwZSkpIHtcclxuICAgICAgICAgICAgICByZXR1cm4gXCJhc3NldHMvc3R5bGVzL1tuYW1lXS1baGFzaF0uW2V4dF1cIjtcclxuICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICByZXR1cm4gXCJhc3NldHMvW2V4dF0vW25hbWVdLVtoYXNoXS5bZXh0XVwiO1xyXG4gICAgICAgICAgfSxcclxuXHJcbiAgICAgICAgICAvLyBcdTYyNEJcdTUyQThcdTUyMDZcdTUyNzJcdTRFRTNcdTc4MDFcdTU3NTdcdUZGMENcdTRGMThcdTUzMTZcdTdGMTNcdTVCNThcdTdCNTZcdTc1NjVcclxuICAgICAgICAgIG1hbnVhbENodW5rczogaWQgPT4ge1xyXG4gICAgICAgICAgICAvLyBcdTY4MzhcdTVGQzNcdTY4NDZcdTY3QjYgLSBcdTUzRDhcdTUzMTZcdTk4OTFcdTczODdcdTRGNEVcdUZGMENcdTUzNTVcdTcyRUNcdTYyNTNcdTUzMDVcdTUyMjlcdTRFOEVcdTk1N0ZcdTY3MUZcdTdGMTNcdTVCNThcclxuICAgICAgICAgICAgaWYgKGlkLmluY2x1ZGVzKFwidnVlXCIpICYmICFpZC5pbmNsdWRlcyhcIm5vZGVfbW9kdWxlc1wiKSkge1xyXG4gICAgICAgICAgICAgIHJldHVybiBcInZ1ZS12ZW5kb3JcIjtcclxuICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICBpZiAoaWQuaW5jbHVkZXMoXCJ2dWUtcm91dGVyXCIpKSB7XHJcbiAgICAgICAgICAgICAgcmV0dXJuIFwidnVlLXZlbmRvclwiO1xyXG4gICAgICAgICAgICB9XHJcbiAgICAgICAgICAgIGlmIChpZC5pbmNsdWRlcyhcInBpbmlhXCIpKSB7XHJcbiAgICAgICAgICAgICAgcmV0dXJuIFwidnVlLXZlbmRvclwiO1xyXG4gICAgICAgICAgICB9XHJcblxyXG4gICAgICAgICAgICAvLyBVSVx1N0VDNFx1NEVGNlx1NUU5MyAtIFx1NEY3Rlx1NzUyOFx1NEUxM1x1OTVFOFx1NzY4NEVsZW1lbnQgUGx1c1x1NTkwNFx1NzQwNlx1OTAzQlx1OEY5MVxyXG4gICAgICAgICAgICBpZiAoaXNFbGVtZW50UGx1c01vZHVsZShpZCkpIHtcclxuICAgICAgICAgICAgICByZXR1cm4gZ2V0RWxlbWVudFBsdXNDaHVua05hbWUoaWQpO1xyXG4gICAgICAgICAgICB9XHJcblxyXG4gICAgICAgICAgICAvLyBcdTU2RkVcdTY4MDdcdTVFOTMgLSBcdTYzMDlcdTk3MDBcdTUyQTBcdThGN0RcdUZGMENcdTUzNTVcdTcyRUNcdTYyNTNcdTUzMDVcclxuICAgICAgICAgICAgaWYgKGlkLmluY2x1ZGVzKFwiQGljb25pZnlcIikpIHtcclxuICAgICAgICAgICAgICByZXR1cm4gXCJpY29ucy12ZW5kb3JcIjtcclxuICAgICAgICAgICAgfVxyXG5cclxuICAgICAgICAgICAgLy8gXHU1REU1XHU1MTc3XHU1RTkzIC0gXHU3NkY4XHU1QkY5XHU3QTMzXHU1QjlBXHJcbiAgICAgICAgICAgIGlmIChpZC5pbmNsdWRlcyhcImF4aW9zXCIpIHx8IGlkLmluY2x1ZGVzKFwiZGF5anNcIikgfHwgaWQuaW5jbHVkZXMoXCJsb2Rhc2hcIikpIHtcclxuICAgICAgICAgICAgICByZXR1cm4gXCJ1dGlscy12ZW5kb3JcIjtcclxuICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICBpZiAoaWQuaW5jbHVkZXMoXCJjcnlwdG8tanNcIikgfHwgaWQuaW5jbHVkZXMoXCJtZDVcIikgfHwgaWQuaW5jbHVkZXMoXCJxc1wiKSkge1xyXG4gICAgICAgICAgICAgIHJldHVybiBcInV0aWxzLXZlbmRvclwiO1xyXG4gICAgICAgICAgICB9XHJcblxyXG4gICAgICAgICAgICAvLyBcdTU2RkVcdTg4NjhcdTVFOTMgLSBcdTU5MjdcdTU3OEJcdTVFOTNcdUZGMENcdTVFRjZcdThGREZcdTUyQTBcdThGN0RcclxuICAgICAgICAgICAgaWYgKGlkLmluY2x1ZGVzKFwiZWNoYXJ0c1wiKSB8fCBpZC5pbmNsdWRlcyhcIkBhbnR2XCIpKSB7XHJcbiAgICAgICAgICAgICAgcmV0dXJuIFwiY2hhcnRzLXZlbmRvclwiO1xyXG4gICAgICAgICAgICB9XHJcblxyXG4gICAgICAgICAgICAvLyBcdTRFRTNcdTc4MDFcdTlBRDhcdTRFQUUgLSBcdTYzMDlcdTk3MDBcdTUyQTBcdThGN0RcclxuICAgICAgICAgICAgaWYgKGlkLmluY2x1ZGVzKFwiaGlnaGxpZ2h0LmpzXCIpIHx8IGlkLmluY2x1ZGVzKFwiQGhpZ2hsaWdodGpzXCIpKSB7XHJcbiAgICAgICAgICAgICAgcmV0dXJuIFwiaGlnaGxpZ2h0LXZlbmRvclwiO1xyXG4gICAgICAgICAgICB9XHJcblxyXG4gICAgICAgICAgICAvLyBcdTU2RkRcdTk2NDVcdTUzMTZcdTc2RjhcdTUxNzNcclxuICAgICAgICAgICAgaWYgKGlkLmluY2x1ZGVzKFwidnVlLWkxOG5cIikpIHtcclxuICAgICAgICAgICAgICByZXR1cm4gXCJpMThuLXZlbmRvclwiO1xyXG4gICAgICAgICAgICB9XHJcblxyXG4gICAgICAgICAgICAvLyBcdTUxNzZcdTRFRDZcdTdCMkNcdTRFMDlcdTY1QjlcdTVFOTNcclxuICAgICAgICAgICAgaWYgKGlkLmluY2x1ZGVzKFwibm9kZV9tb2R1bGVzXCIpKSB7XHJcbiAgICAgICAgICAgICAgcmV0dXJuIFwidmVuZG9yXCI7XHJcbiAgICAgICAgICAgIH1cclxuXHJcbiAgICAgICAgICAgIC8vIFx1NEUxQVx1NTJBMVx1NEVFM1x1NzgwMVx1NjMwOVx1NkEyMVx1NTc1N1x1NTIwNlx1NTI3MlxyXG4gICAgICAgICAgICBpZiAoaWQuaW5jbHVkZXMoXCJzcmMvdmlld3MvXCIpKSB7XHJcbiAgICAgICAgICAgICAgY29uc3QgbWF0Y2ggPSBpZC5tYXRjaCgvc3JjXFwvdmlld3NcXC8oW15cXC9dKykvKTtcclxuICAgICAgICAgICAgICBpZiAobWF0Y2gpIHtcclxuICAgICAgICAgICAgICAgIHJldHVybiBgdmlld3MtJHttYXRjaFsxXX1gO1xyXG4gICAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgfVxyXG5cclxuICAgICAgICAgICAgLy8gXHU3RUM0XHU0RUY2XHU1RTkzXHJcbiAgICAgICAgICAgIGlmIChpZC5pbmNsdWRlcyhcInNyYy9jb21wb25lbnRzL1wiKSkge1xyXG4gICAgICAgICAgICAgIHJldHVybiBcImNvbXBvbmVudHNcIjtcclxuICAgICAgICAgICAgfVxyXG5cclxuICAgICAgICAgICAgLy8gXHU1REU1XHU1MTc3XHU1MUZEXHU2NTcwXHJcbiAgICAgICAgICAgIGlmIChpZC5pbmNsdWRlcyhcInNyYy91dGlscy9cIikgfHwgaWQuaW5jbHVkZXMoXCJzcmMvaG9va3MvXCIpKSB7XHJcbiAgICAgICAgICAgICAgcmV0dXJuIFwidXRpbHNcIjtcclxuICAgICAgICAgICAgfVxyXG4gICAgICAgICAgfVxyXG4gICAgICAgIH1cclxuICAgICAgfVxyXG4gICAgfVxyXG4gIH07XHJcbn0pO1xyXG4iLCAiY29uc3QgX192aXRlX2luamVjdGVkX29yaWdpbmFsX2Rpcm5hbWUgPSBcIkU6XFxcXFx1NURFNVx1NTE3N1x1OEY2Rlx1NEVGNlxcXFx2aXN1YWxkZWJ1Z1xcXFxmcm9udGVuZFxcXFxidWlsZFwiO2NvbnN0IF9fdml0ZV9pbmplY3RlZF9vcmlnaW5hbF9maWxlbmFtZSA9IFwiRTpcXFxcXHU1REU1XHU1MTc3XHU4RjZGXHU0RUY2XFxcXHZpc3VhbGRlYnVnXFxcXGZyb250ZW5kXFxcXGJ1aWxkXFxcXGdldEVudi50c1wiO2NvbnN0IF9fdml0ZV9pbmplY3RlZF9vcmlnaW5hbF9pbXBvcnRfbWV0YV91cmwgPSBcImZpbGU6Ly8vRTovJUU1JUI3JUE1JUU1JTg1JUI3JUU4JUJEJUFGJUU0JUJCJUI2L3Zpc3VhbGRlYnVnL2Zyb250ZW5kL2J1aWxkL2dldEVudi50c1wiO2ltcG9ydCBwYXRoIGZyb20gXCJwYXRoXCI7XG5cbmV4cG9ydCBmdW5jdGlvbiBpc0RldkZuKG1vZGU6IHN0cmluZyk6IGJvb2xlYW4ge1xuICByZXR1cm4gbW9kZSA9PT0gXCJkZXZlbG9wbWVudFwiO1xufVxuXG5leHBvcnQgZnVuY3Rpb24gaXNQcm9kRm4obW9kZTogc3RyaW5nKTogYm9vbGVhbiB7XG4gIHJldHVybiBtb2RlID09PSBcInByb2R1Y3Rpb25cIjtcbn1cblxuZXhwb3J0IGZ1bmN0aW9uIGlzVGVzdEZuKG1vZGU6IHN0cmluZyk6IGJvb2xlYW4ge1xuICByZXR1cm4gbW9kZSA9PT0gXCJ0ZXN0XCI7XG59XG5cbi8qKlxuICogV2hldGhlciB0byBnZW5lcmF0ZSBwYWNrYWdlIHByZXZpZXdcbiAqL1xuZXhwb3J0IGZ1bmN0aW9uIGlzUmVwb3J0TW9kZSgpOiBib29sZWFuIHtcbiAgcmV0dXJuIHByb2Nlc3MuZW52LlZJVEVfUkVQT1JUID09PSBcInRydWVcIjtcbn1cblxuLy8gUmVhZCBhbGwgZW52aXJvbm1lbnQgdmFyaWFibGUgY29uZmlndXJhdGlvbiBmaWxlcyB0byBwcm9jZXNzLmVudlxuZXhwb3J0IGZ1bmN0aW9uIHdyYXBwZXJFbnYoZW52Q29uZjogUmVjb3JkYWJsZSk6IFZpdGVFbnYge1xuICBjb25zdCByZXQ6IGFueSA9IHt9O1xuXG4gIGZvciAoY29uc3QgZW52TmFtZSBvZiBPYmplY3Qua2V5cyhlbnZDb25mKSkge1xuICAgIGxldCByZWFsTmFtZSA9IGVudkNvbmZbZW52TmFtZV0ucmVwbGFjZSgvXFxcXG4vZywgXCJcXG5cIik7XG4gICAgcmVhbE5hbWUgPSByZWFsTmFtZSA9PT0gXCJ0cnVlXCIgPyB0cnVlIDogcmVhbE5hbWUgPT09IFwiZmFsc2VcIiA/IGZhbHNlIDogcmVhbE5hbWU7XG4gICAgaWYgKGVudk5hbWUgPT09IFwiVklURV9QT1JUXCIpIHJlYWxOYW1lID0gTnVtYmVyKHJlYWxOYW1lKTtcbiAgICBpZiAoZW52TmFtZSA9PT0gXCJWSVRFX1BST1hZXCIpIHtcbiAgICAgIHRyeSB7XG4gICAgICAgIHJlYWxOYW1lID0gSlNPTi5wYXJzZShyZWFsTmFtZSk7XG4gICAgICB9IGNhdGNoIChlcnJvcikge31cbiAgICB9XG4gICAgcmV0W2Vudk5hbWVdID0gcmVhbE5hbWU7XG4gIH1cbiAgcmV0dXJuIHJldDtcbn1cblxuLyoqXG4gKiBHZXQgdXNlciByb290IGRpcmVjdG9yeVxuICogQHBhcmFtIGRpciBmaWxlIHBhdGhcbiAqL1xuZXhwb3J0IGZ1bmN0aW9uIGdldFJvb3RQYXRoKC4uLmRpcjogc3RyaW5nW10pIHtcbiAgcmV0dXJuIHBhdGgucmVzb2x2ZShwcm9jZXNzLmN3ZCgpLCAuLi5kaXIpO1xufVxuIiwgImNvbnN0IF9fdml0ZV9pbmplY3RlZF9vcmlnaW5hbF9kaXJuYW1lID0gXCJFOlxcXFxcdTVERTVcdTUxNzdcdThGNkZcdTRFRjZcXFxcdmlzdWFsZGVidWdcXFxcZnJvbnRlbmRcXFxcYnVpbGRcIjtjb25zdCBfX3ZpdGVfaW5qZWN0ZWRfb3JpZ2luYWxfZmlsZW5hbWUgPSBcIkU6XFxcXFx1NURFNVx1NTE3N1x1OEY2Rlx1NEVGNlxcXFx2aXN1YWxkZWJ1Z1xcXFxmcm9udGVuZFxcXFxidWlsZFxcXFxwcm94eS50c1wiO2NvbnN0IF9fdml0ZV9pbmplY3RlZF9vcmlnaW5hbF9pbXBvcnRfbWV0YV91cmwgPSBcImZpbGU6Ly8vRTovJUU1JUI3JUE1JUU1JTg1JUI3JUU4JUJEJUFGJUU0JUJCJUI2L3Zpc3VhbGRlYnVnL2Zyb250ZW5kL2J1aWxkL3Byb3h5LnRzXCI7aW1wb3J0IHR5cGUgeyBQcm94eU9wdGlvbnMgfSBmcm9tIFwidml0ZVwiO1xuXG50eXBlIFByb3h5SXRlbSA9IFtzdHJpbmcsIHN0cmluZ107XG5cbnR5cGUgUHJveHlMaXN0ID0gUHJveHlJdGVtW107XG5cbnR5cGUgUHJveHlUYXJnZXRMaXN0ID0gUmVjb3JkPHN0cmluZywgUHJveHlPcHRpb25zPjtcblxuLyoqXG4gKiBcdTUyMUJcdTVFRkFcdTRFRTNcdTc0MDZcdUZGMENcdTc1MjhcdTRFOEVcdTg5RTNcdTY3OTAgLmVudi5kZXZlbG9wbWVudCBcdTRFRTNcdTc0MDZcdTkxNERcdTdGNkVcbiAqIEBwYXJhbSBsaXN0XG4gKi9cbmV4cG9ydCBmdW5jdGlvbiBjcmVhdGVQcm94eShsaXN0OiBQcm94eUxpc3QgPSBbXSkge1xuICBjb25zdCByZXQ6IFByb3h5VGFyZ2V0TGlzdCA9IHt9O1xuICBmb3IgKGNvbnN0IFtwcmVmaXgsIHRhcmdldF0gb2YgbGlzdCkge1xuICAgIGNvbnN0IGh0dHBzUkUgPSAvXmh0dHBzOlxcL1xcLy87XG4gICAgY29uc3QgaXNIdHRwcyA9IGh0dHBzUkUudGVzdCh0YXJnZXQpO1xuXG4gICAgLy8gaHR0cHM6Ly9naXRodWIuY29tL2h0dHAtcGFydHkvbm9kZS1odHRwLXByb3h5I29wdGlvbnNcbiAgICByZXRbcHJlZml4XSA9IHtcbiAgICAgIHRhcmdldDogdGFyZ2V0LFxuICAgICAgY2hhbmdlT3JpZ2luOiB0cnVlLFxuICAgICAgd3M6IHRydWUsXG4gICAgICByZXdyaXRlOiBwYXRoID0+IHBhdGgucmVwbGFjZShuZXcgUmVnRXhwKGBeJHtwcmVmaXh9YCksIFwiXCIpLFxuICAgICAgLy8gaHR0cHMgaXMgcmVxdWlyZSBzZWN1cmU9ZmFsc2VcbiAgICAgIC4uLihpc0h0dHBzID8geyBzZWN1cmU6IGZhbHNlIH0gOiB7fSlcbiAgICB9O1xuICB9XG4gIHJldHVybiByZXQ7XG59XG4iLCAiY29uc3QgX192aXRlX2luamVjdGVkX29yaWdpbmFsX2Rpcm5hbWUgPSBcIkU6XFxcXFx1NURFNVx1NTE3N1x1OEY2Rlx1NEVGNlxcXFx2aXN1YWxkZWJ1Z1xcXFxmcm9udGVuZFxcXFxidWlsZFwiO2NvbnN0IF9fdml0ZV9pbmplY3RlZF9vcmlnaW5hbF9maWxlbmFtZSA9IFwiRTpcXFxcXHU1REU1XHU1MTc3XHU4RjZGXHU0RUY2XFxcXHZpc3VhbGRlYnVnXFxcXGZyb250ZW5kXFxcXGJ1aWxkXFxcXHBsdWdpbnMudHNcIjtjb25zdCBfX3ZpdGVfaW5qZWN0ZWRfb3JpZ2luYWxfaW1wb3J0X21ldGFfdXJsID0gXCJmaWxlOi8vL0U6LyVFNSVCNyVBNSVFNSU4NSVCNyVFOCVCRCVBRiVFNCVCQiVCNi92aXN1YWxkZWJ1Zy9mcm9udGVuZC9idWlsZC9wbHVnaW5zLnRzXCI7aW1wb3J0IHsgcmVzb2x2ZSB9IGZyb20gXCJwYXRoXCI7XHJcbmltcG9ydCB7IFBsdWdpbk9wdGlvbiB9IGZyb20gXCJ2aXRlXCI7XHJcbmltcG9ydCB7IFZpdGVQV0EgfSBmcm9tIFwidml0ZS1wbHVnaW4tcHdhXCI7XHJcbmltcG9ydCB7IHZpc3VhbGl6ZXIgfSBmcm9tIFwicm9sbHVwLXBsdWdpbi12aXN1YWxpemVyXCI7XHJcbmltcG9ydCB7IGNyZWF0ZVN2Z0ljb25zUGx1Z2luIH0gZnJvbSBcInZpdGUtcGx1Z2luLXN2Zy1pY29uc1wiO1xyXG5pbXBvcnQgeyBjcmVhdGVIdG1sUGx1Z2luIH0gZnJvbSBcInZpdGUtcGx1Z2luLWh0bWxcIjtcclxuaW1wb3J0IHZ1ZSBmcm9tIFwiQHZpdGVqcy9wbHVnaW4tdnVlXCI7XHJcbmltcG9ydCB2dWVKc3ggZnJvbSBcIkB2aXRlanMvcGx1Z2luLXZ1ZS1qc3hcIjtcclxuaW1wb3J0IGVzbGludFBsdWdpbiBmcm9tIFwidml0ZS1wbHVnaW4tZXNsaW50XCI7XHJcbmltcG9ydCB2aXRlQ29tcHJlc3Npb24gZnJvbSBcInZpdGUtcGx1Z2luLWNvbXByZXNzaW9uXCI7XHJcbmltcG9ydCB2dWVTZXR1cEV4dGVuZCBmcm9tIFwidW5wbHVnaW4tdnVlLXNldHVwLWV4dGVuZC1wbHVzL3ZpdGVcIjtcclxuaW1wb3J0IFVub0NTUyBmcm9tIFwidW5vY3NzL3ZpdGVcIjtcclxuaW1wb3J0IEF1dG9JbXBvcnQgZnJvbSBcInVucGx1Z2luLWF1dG8taW1wb3J0L3ZpdGVcIjtcclxuaW1wb3J0IHsgRWxlbWVudFBsdXNSZXNvbHZlciB9IGZyb20gXCJ1bnBsdWdpbi12dWUtY29tcG9uZW50cy9yZXNvbHZlcnNcIjtcclxuaW1wb3J0IENvbXBvbmVudHMgZnJvbSBcInVucGx1Z2luLXZ1ZS1jb21wb25lbnRzL3ZpdGVcIjtcclxuaW1wb3J0IEljb25zIGZyb20gXCJ1bnBsdWdpbi1pY29ucy92aXRlXCI7XHJcbmltcG9ydCBJY29uc1Jlc29sdmVyIGZyb20gXCJ1bnBsdWdpbi1pY29ucy9yZXNvbHZlclwiO1xyXG5pbXBvcnQgTmV4dERldlRvb2xzIGZyb20gXCJ2aXRlLXBsdWdpbi12dWUtZGV2dG9vbHNcIjtcclxuaW1wb3J0IHsgY29kZUluc3BlY3RvclBsdWdpbiB9IGZyb20gXCJjb2RlLWluc3BlY3Rvci1wbHVnaW5cIjtcclxuLyoqXHJcbiAqIFx1NTIxQlx1NUVGQSB2aXRlIFx1NjNEMlx1NEVGNlxyXG4gKiBAcGFyYW0gdml0ZUVudlxyXG4gKi9cclxuZXhwb3J0IGNvbnN0IGNyZWF0ZVZpdGVQbHVnaW5zID0gKHZpdGVFbnY6IFZpdGVFbnYpOiAoUGx1Z2luT3B0aW9uIHwgUGx1Z2luT3B0aW9uW10pW10gPT4ge1xyXG4gIGNvbnN0IHsgVklURV9HTE9CX0FQUF9USVRMRSwgVklURV9SRVBPUlQsIFZJVEVfREVWVE9PTFMsIFZJVEVfUFdBLCBWSVRFX0NPREVJTlNQRUNUT1IgfSA9IHZpdGVFbnY7XHJcblxyXG4gIHJldHVybiBbXHJcbiAgICB2dWUoKSxcclxuICAgIC8vIGRldlRvb2xzXHJcbiAgICBWSVRFX0RFVlRPT0xTICYmIE5leHREZXZUb29scyh7IGxhdW5jaEVkaXRvcjogXCJjb2RlXCIgfSksXHJcbiAgICAvLyB2dWUgXHU1M0VGXHU0RUU1XHU0RjdGXHU3NTI4IGpzeC90c3ggXHU4QkVEXHU2Q0Q1XHJcbiAgICB2dWVKc3goKSxcclxuICAgIC8vIGVzTGludCBcdTYyQTVcdTk1MTlcdTRGRTFcdTYwNkZcdTY2M0VcdTc5M0FcdTU3MjhcdTZENEZcdTg5QzhcdTU2NjhcdTc1NENcdTk3NjJcdTRFMEFcclxuICAgIGVzbGludFBsdWdpbigpLFxyXG4gICAgLy8gbmFtZSBcdTUzRUZcdTRFRTVcdTUxOTlcdTU3Mjggc2NyaXB0IFx1NjgwN1x1N0I3RVx1NEUwQVxyXG4gICAgdnVlU2V0dXBFeHRlbmQoe30pLFxyXG4gICAgLy8gXHU1MjFCXHU1RUZBXHU2MjUzXHU1MzA1XHU1MzhCXHU3RjI5XHU5MTREXHU3RjZFXHJcbiAgICBjcmVhdGVDb21wcmVzc2lvbih2aXRlRW52KSxcclxuICAgIC8vIFx1NkNFOFx1NTE2NVx1NTNEOFx1OTFDRlx1NTIzMCBodG1sIFx1NjU4N1x1NEVGNlxyXG4gICAgY3JlYXRlSHRtbFBsdWdpbih7XHJcbiAgICAgIG1pbmlmeTogdHJ1ZSxcclxuICAgICAgaW5qZWN0OiB7XHJcbiAgICAgICAgZGF0YTogeyB0aXRsZTogVklURV9HTE9CX0FQUF9USVRMRSB9XHJcbiAgICAgIH1cclxuICAgIH0pLFxyXG4gICAgLy8gXHU0RjdGXHU3NTI4IHN2ZyBcdTU2RkVcdTY4MDdcclxuICAgIGNyZWF0ZVN2Z0ljb25zUGx1Z2luKHtcclxuICAgICAgaWNvbkRpcnM6IFtyZXNvbHZlKHByb2Nlc3MuY3dkKCksIFwic3JjL2Fzc2V0cy9zdmdcIildLFxyXG4gICAgICBzeW1ib2xJZDogXCJsb2NhbC1bZGlyXS1bbmFtZV1cIlxyXG4gICAgfSksXHJcbiAgICAvLyB2aXRlUFdBXHJcbiAgICBWSVRFX1BXQSAmJiBjcmVhdGVWaXRlUHdhKHZpdGVFbnYpLFxyXG4gICAgLy8gXHU2NjJGXHU1NDI2XHU3NTFGXHU2MjEwXHU1MzA1XHU5ODg0XHU4OUM4XHVGRjBDXHU1MjA2XHU2NzkwXHU0RjlEXHU4RDU2XHU1MzA1XHU1OTI3XHU1QzBGXHU1MDVBXHU0RjE4XHU1MzE2XHU1OTA0XHU3NDA2XHJcbiAgICBWSVRFX1JFUE9SVCAmJiAodmlzdWFsaXplcih7IGZpbGVuYW1lOiBcInN0YXRzLmh0bWxcIiwgZ3ppcFNpemU6IHRydWUsIGJyb3RsaVNpemU6IHRydWUgfSkgYXMgUGx1Z2luT3B0aW9uKSxcclxuICAgIC8vIFx1ODFFQVx1NTJBOCBJREUgXHU1RTc2XHU1QzA2XHU1MTQ5XHU2ODA3XHU1QjlBXHU0RjREXHU1MjMwIERPTSBcdTVCRjlcdTVFOTRcdTc2ODRcdTZFOTBcdTRFRTNcdTc4MDFcdTRGNERcdTdGNkVcdTMwMDJzZWU6IGh0dHBzOi8vaW5zcGVjdG9yLmZlLWRldi5jbi9ndWlkZS9zdGFydC5odG1sXHJcbiAgICBWSVRFX0NPREVJTlNQRUNUT1IgJiZcclxuICAgICAgY29kZUluc3BlY3RvclBsdWdpbih7XHJcbiAgICAgICAgYnVuZGxlcjogXCJ2aXRlXCJcclxuICAgICAgfSksXHJcbiAgICAvLyBcdTgxRUFcdTUyQThcdTVCRkNcdTUxNjVcdTdFQzRcdTRFRjZcclxuICAgIEF1dG9JbXBvcnQoe1xyXG4gICAgICBpbXBvcnRzOiBbXCJ2dWVcIiwgXCJ2dWUtcm91dGVyXCJdLFxyXG4gICAgICAvLyBBdXRvIGltcG9ydCBmdW5jdGlvbnMgZnJvbSBFbGVtZW50IFBsdXMsIGUuZy4gRWxNZXNzYWdlLCBFbE1lc3NhZ2VCb3guLi4gKHdpdGhvdXQgc3R5bGUpXHJcbiAgICAgIC8vIFx1ODFFQVx1NTJBOFx1NUJGQ1x1NTE2NSBFbGVtZW50IFBsdXMgXHU3NkY4XHU1MTczXHU1MUZEXHU2NTcwXHVGRjBDXHU1OTgyXHVGRjFBRWxNZXNzYWdlLCBFbE1lc3NhZ2VCb3guLi4gKFx1NEUwRFx1NUUyNlx1NjgzN1x1NUYwRlx1RkYwQ1x1NTZFMFx1NEUzQVx1NURGMlx1NTcyOG1haW4udHNcdTRFMkRcdTVCRkNcdTUxNjVcdTVCOENcdTY1NzRcdTY4MzdcdTVGMEYpXHJcbiAgICAgIHJlc29sdmVyczogW1xyXG4gICAgICAgIEVsZW1lbnRQbHVzUmVzb2x2ZXIoe1xyXG4gICAgICAgICAgLy8gXHU3OTgxXHU3NTI4XHU2ODM3XHU1RjBGXHU4MUVBXHU1MkE4XHU1QkZDXHU1MTY1XHVGRjBDXHU1NkUwXHU0RTNBXHU1REYyXHU1NzI4bWFpbi50c1x1NEUyRFx1NUJGQ1x1NTE2NVx1NUI4Q1x1NjU3NFx1NjgzN1x1NUYwRlxyXG4gICAgICAgICAgaW1wb3J0U3R5bGU6IGZhbHNlXHJcbiAgICAgICAgfSksXHJcblxyXG4gICAgICAgIC8vIEF1dG8gaW1wb3J0IGljb24gY29tcG9uZW50c1xyXG4gICAgICAgIC8vIFx1ODFFQVx1NTJBOFx1NUJGQ1x1NTE2NVx1NTZGRVx1NjgwN1x1N0VDNFx1NEVGNlxyXG4gICAgICAgIEljb25zUmVzb2x2ZXIoe1xyXG4gICAgICAgICAgcHJlZml4OiBcIkljb25cIlxyXG4gICAgICAgIH0pXHJcbiAgICAgIF0sXHJcbiAgICAgIGR0czogXCJzcmMvYXV0by1pbXBvcnQuZC50c1wiIC8vIFx1OERFRlx1NUY4NFx1NEUwQlx1ODFFQVx1NTJBOFx1NzUxRlx1NjIxMFx1NjU4N1x1NEVGNlx1NTkzOVx1NUI1OFx1NjUzRVx1NTE2OFx1NUM0MFx1NjMwN1x1NEVFNFxyXG4gICAgfSksXHJcbiAgICBDb21wb25lbnRzKHtcclxuICAgICAgZGlyczogW1wic3JjL2NvbXBvbmVudHNcIl0sIC8vIFx1OTE0RFx1N0Y2RVx1OTcwMFx1ODk4MVx1OUVEOFx1OEJBNFx1NUJGQ1x1NTE2NVx1NzY4NFx1ODFFQVx1NUI5QVx1NEU0OVx1N0VDNFx1NEVGNlx1NjU4N1x1NEVGNlx1NTkzOVx1RkYwQ1x1OEJFNVx1NjU4N1x1NEVGNlx1NTkzOVx1NEUwQlx1NzY4NFx1NjI0MFx1NjcwOVx1N0VDNFx1NEVGNlx1OTBGRFx1NEYxQVx1ODFFQVx1NTJBOCBpbXBvcnRcclxuICAgICAgcmVzb2x2ZXJzOiBbXHJcbiAgICAgICAgLy8gQXV0byByZWdpc3RlciBpY29uIGNvbXBvbmVudHNcclxuICAgICAgICAvLyBcdTgxRUFcdTUyQThcdTZDRThcdTUxOENcdTU2RkVcdTY4MDdcdTdFQzRcdTRFRjZcclxuICAgICAgICBJY29uc1Jlc29sdmVyKHtcclxuICAgICAgICAgIGVuYWJsZWRDb2xsZWN0aW9uczogW1wiZXBcIl0gLy8gZWxlbWVudC1wbHVzIFx1NTZGRVx1NjgwN1x1NUU5M1xyXG4gICAgICAgIH0pLFxyXG4gICAgICAgIC8vIEF1dG8gcmVnaXN0ZXIgRWxlbWVudCBQbHVzIGNvbXBvbmVudHNcclxuICAgICAgICAvLyBcdTgxRUFcdTUyQThcdTVCRkNcdTUxNjUgRWxlbWVudCBQbHVzIFx1N0VDNFx1NEVGNlx1RkYwOFx1NEUwRFx1NUUyNlx1NjgzN1x1NUYwRlx1RkYwQ1x1NTZFMFx1NEUzQVx1NURGMlx1NTcyOG1haW4udHNcdTRFMkRcdTVCRkNcdTUxNjVcdTVCOENcdTY1NzRcdTY4MzdcdTVGMEZcdUZGMDlcclxuICAgICAgICBFbGVtZW50UGx1c1Jlc29sdmVyKHtcclxuICAgICAgICAgIC8vIFx1Nzk4MVx1NzUyOFx1NjgzN1x1NUYwRlx1ODFFQVx1NTJBOFx1NUJGQ1x1NTE2NVx1RkYwQ1x1NTZFMFx1NEUzQVx1NURGMlx1NTcyOG1haW4udHNcdTRFMkRcdTVCRkNcdTUxNjVcdTVCOENcdTY1NzRcdTY4MzdcdTVGMEZcclxuICAgICAgICAgIGltcG9ydFN0eWxlOiBmYWxzZSxcclxuICAgICAgICAgIC8vIFx1Nzg2RVx1NEZERFx1NjQxQ1x1N0QyMlx1ODg2OFx1NTM1NVx1N0VDNFx1NEVGNlx1ODBGRFx1ODhBQlx1NkI2M1x1Nzg2RVx1ODlFM1x1Njc5MFxyXG4gICAgICAgICAgcmVzb2x2ZUljb25zOiB0cnVlXHJcbiAgICAgICAgfSlcclxuICAgICAgXSxcclxuICAgICAgLy8gXHU3NTFGXHU2MjEwXHU3QzdCXHU1NzhCXHU1OEYwXHU2NjBFXHU2NTg3XHU0RUY2XHJcbiAgICAgIGR0czogdHJ1ZVxyXG4gICAgfSksXHJcbiAgICBJY29ucyh7XHJcbiAgICAgIGNvbXBpbGVyOiBcInZ1ZTNcIixcclxuICAgICAgYXV0b0luc3RhbGw6IHRydWVcclxuICAgIH0pLFxyXG5cclxuICAgIFVub0NTUygpIC8vIFVub0NTU1xyXG4gIF07XHJcbn07XHJcblxyXG4vKipcclxuICogQGRlc2NyaXB0aW9uIFx1NjgzOVx1NjM2RSBjb21wcmVzcyBcdTkxNERcdTdGNkVcdUZGMENcdTc1MUZcdTYyMTBcdTRFMERcdTU0MENcdTc2ODRcdTUzOEJcdTdGMjlcdTg5QzRcdTUyMTlcclxuICogQHBhcmFtIHZpdGVFbnZcclxuICovXHJcbmNvbnN0IGNyZWF0ZUNvbXByZXNzaW9uID0gKHZpdGVFbnY6IFZpdGVFbnYpOiBQbHVnaW5PcHRpb24gfCBQbHVnaW5PcHRpb25bXSA9PiB7XHJcbiAgY29uc3QgeyBWSVRFX0JVSUxEX0NPTVBSRVNTID0gXCJub25lXCIsIFZJVEVfQlVJTERfQ09NUFJFU1NfREVMRVRFX09SSUdJTl9GSUxFIH0gPSB2aXRlRW52O1xyXG4gIGNvbnN0IGNvbXByZXNzTGlzdCA9IFZJVEVfQlVJTERfQ09NUFJFU1Muc3BsaXQoXCIsXCIpO1xyXG4gIGNvbnN0IHBsdWdpbnM6IFBsdWdpbk9wdGlvbltdID0gW107XHJcbiAgaWYgKGNvbXByZXNzTGlzdC5pbmNsdWRlcyhcImd6aXBcIikpIHtcclxuICAgIHBsdWdpbnMucHVzaChcclxuICAgICAgdml0ZUNvbXByZXNzaW9uKHtcclxuICAgICAgICBleHQ6IFwiLmd6XCIsXHJcbiAgICAgICAgYWxnb3JpdGhtOiBcImd6aXBcIixcclxuICAgICAgICBkZWxldGVPcmlnaW5GaWxlOiBWSVRFX0JVSUxEX0NPTVBSRVNTX0RFTEVURV9PUklHSU5fRklMRVxyXG4gICAgICB9KVxyXG4gICAgKTtcclxuICB9XHJcbiAgaWYgKGNvbXByZXNzTGlzdC5pbmNsdWRlcyhcImJyb3RsaVwiKSkge1xyXG4gICAgcGx1Z2lucy5wdXNoKFxyXG4gICAgICB2aXRlQ29tcHJlc3Npb24oe1xyXG4gICAgICAgIGV4dDogXCIuYnJcIixcclxuICAgICAgICBhbGdvcml0aG06IFwiYnJvdGxpQ29tcHJlc3NcIixcclxuICAgICAgICBkZWxldGVPcmlnaW5GaWxlOiBWSVRFX0JVSUxEX0NPTVBSRVNTX0RFTEVURV9PUklHSU5fRklMRVxyXG4gICAgICB9KVxyXG4gICAgKTtcclxuICB9XHJcbiAgcmV0dXJuIHBsdWdpbnM7XHJcbn07XHJcblxyXG4vKipcclxuICogQGRlc2NyaXB0aW9uIFZpdGVQd2FcclxuICogQHBhcmFtIHZpdGVFbnZcclxuICovXHJcbmNvbnN0IGNyZWF0ZVZpdGVQd2EgPSAodml0ZUVudjogVml0ZUVudik6IFBsdWdpbk9wdGlvbiB8IFBsdWdpbk9wdGlvbltdID0+IHtcclxuICBjb25zdCB7IFZJVEVfR0xPQl9BUFBfVElUTEUgfSA9IHZpdGVFbnY7XHJcbiAgcmV0dXJuIFZpdGVQV0Eoe1xyXG4gICAgcmVnaXN0ZXJUeXBlOiBcImF1dG9VcGRhdGVcIixcclxuICAgIHdvcmtib3g6IHtcclxuICAgICAgLy8gXHU2REZCXHU1MkEwXHU2QjY0XHU5ODc5XHU5MTREXHU3RjZFXHVGRjBDXHU1ODlFXHU1MkEwXHU5NzAwXHU4OTgxXHU3RjEzXHU1QjU4XHU3Njg0XHU2NzAwXHU1OTI3XHU2NTg3XHU0RUY2XHU1OTI3XHU1QzBGXHJcbiAgICAgIG1heGltdW1GaWxlU2l6ZVRvQ2FjaGVJbkJ5dGVzOiA2ICogMTAyNCAqIDEwMjRcclxuICAgIH0sXHJcbiAgICBtYW5pZmVzdDoge1xyXG4gICAgICBuYW1lOiBWSVRFX0dMT0JfQVBQX1RJVExFLFxyXG4gICAgICBzaG9ydF9uYW1lOiBWSVRFX0dMT0JfQVBQX1RJVExFLFxyXG4gICAgICB0aGVtZV9jb2xvcjogXCIjZmZmZmZmXCIsXHJcbiAgICAgIGljb25zOiBbXHJcbiAgICAgICAge1xyXG4gICAgICAgICAgc3JjOiBcIi9sb2dvLnBuZ1wiLFxyXG4gICAgICAgICAgc2l6ZXM6IFwiMTkyeDE5MlwiLFxyXG4gICAgICAgICAgdHlwZTogXCJpbWFnZS9wbmdcIlxyXG4gICAgICAgIH0sXHJcbiAgICAgICAge1xyXG4gICAgICAgICAgc3JjOiBcIi9sb2dvLnBuZ1wiLFxyXG4gICAgICAgICAgc2l6ZXM6IFwiNTEyeDUxMlwiLFxyXG4gICAgICAgICAgdHlwZTogXCJpbWFnZS9wbmdcIlxyXG4gICAgICAgIH0sXHJcbiAgICAgICAge1xyXG4gICAgICAgICAgc3JjOiBcIi9sb2dvLnBuZ1wiLFxyXG4gICAgICAgICAgc2l6ZXM6IFwiNTEyeDUxMlwiLFxyXG4gICAgICAgICAgdHlwZTogXCJpbWFnZS9wbmdcIixcclxuICAgICAgICAgIHB1cnBvc2U6IFwiYW55IG1hc2thYmxlXCJcclxuICAgICAgICB9XHJcbiAgICAgIF1cclxuICAgIH1cclxuICB9KTtcclxufTtcclxuIiwgIntcclxuICBcIm5hbWVcIjogXCJWaXN1YWxEZWJ1Z1wiLFxyXG4gIFwicHJpdmF0ZVwiOiB0cnVlLFxyXG4gIFwidHlwZVwiOiBcIm1vZHVsZVwiLFxyXG4gIFwiZGVzY3JpcHRpb25cIjogXCJcdTUzRUZcdTg5QzZcdTUzMTZcdTVFNzNcdTUzRjBcdTVERTVcdTdBMEJcdThDMDNcdThCRDVcdTVERTVcdTUxNzdcIixcclxuICBcImxpY2Vuc2VcIjogXCJNSVRcIixcclxuICBcInNjcmlwdHNcIjoge1xyXG4gICAgXCJkZXZcIjogXCJ2aXRlIC0taG9zdCAtLXBvcnQgODA4MFwiLFxyXG4gICAgXCJkZXY6Zm9yY2VcIjogXCJ2aXRlIC0taG9zdCAtLXBvcnQgODA4MCAtLWZvcmNlXCIsXHJcbiAgICBcImJ1aWxkXCI6IFwibm9kZSAuL25vZGVfbW9kdWxlcy92aXRlL2Jpbi92aXRlLmpzIGJ1aWxkXCIsXHJcbiAgICBcImJ1aWxkOmRldlwiOiBcInZ1ZS10c2MgJiYgdml0ZSBidWlsZCAtLW1vZGUgZGV2ZWxvcG1lbnRcIixcclxuICAgIFwiYnVpbGQ6dGVzdFwiOiBcInZ1ZS10c2MgJiYgdml0ZSBidWlsZCAtLW1vZGUgdGVzdFwiLFxyXG4gICAgXCJidWlsZDpwcm9cIjogXCJ2dWUtdHNjICYmIHZpdGUgYnVpbGQgLS1tb2RlIHByb2R1Y3Rpb25cIixcclxuICAgIFwiYnVpbGQ6YW5hbHl6ZVwiOiBcInZpdGUgYnVpbGQgLS1tb2RlIHByb2R1Y3Rpb24gJiYgbnB4IHZpdGUtYnVuZGxlLWFuYWx5emVyIGRpc3Qvc3RhdHMuaHRtbFwiLFxyXG4gICAgXCJ0eXBlOmNoZWNrXCI6IFwidnVlLXRzYyAtLW5vRW1pdCAtLXNraXBMaWJDaGVja1wiLFxyXG4gICAgXCJwcmV2aWV3XCI6IFwicG5wbSBydW4gYnVpbGQ6ZGV2ICYmIHZpdGUgcHJldmlld1wiLFxyXG4gICAgXCJsaW50OmVzbGludFwiOiBcImVzbGludCAtLWZpeCAtLWV4dCAuanMsLnRzLC52dWUgLi9zcmNcIixcclxuICAgIFwibGludDpwcmV0dGllclwiOiBcInByZXR0aWVyIC0td3JpdGUgXFxcInNyYy8qKi8qLntqcyx0cyxqc29uLHRzeCxjc3MsbGVzcyxzY3NzLHZ1ZSxodG1sLG1kfVxcXCJcIixcclxuICAgIFwibGludDpzdHlsZWxpbnRcIjogXCJzdHlsZWxpbnQgLS1jYWNoZSAtLWZpeCBcXFwiKiovKi57dnVlLGxlc3MscG9zdGNzcyxjc3Msc2Nzc31cXFwiIC0tY2FjaGUgLS1jYWNoZS1sb2NhdGlvbiBub2RlX21vZHVsZXMvLmNhY2hlL3N0eWxlbGludC9cIixcclxuICAgIFwibGludDpsaW50LXN0YWdlZFwiOiBcImxpbnQtc3RhZ2VkXCIsXHJcbiAgICBcImNhY2hlOmNsZWFyXCI6IFwibm9kZSBzY3JpcHRzL2NsZWFyLWNhY2hlLmpzXCIsXHJcbiAgICBcImNhY2hlOmNsZWFyOmRldlwiOiBcIm5vZGUgc2NyaXB0cy9jbGVhci1jYWNoZS5qcyAtLWRldlwiLFxyXG4gICAgXCJjYWNoZTpjbGVhcjpidWlsZFwiOiBcIm5vZGUgc2NyaXB0cy9jbGVhci1jYWNoZS5qcyAtLWJ1aWxkXCIsXHJcbiAgICBcImNhY2hlOmNsZWFyOmFsbFwiOiBcIm5vZGUgc2NyaXB0cy9jbGVhci1jYWNoZS5qcyAtLWFsbFwiLFxyXG4gICAgXCJkZXBzOnVwZGF0ZVwiOiBcIm5wbSB1cGRhdGUgJiYgbnBtIGF1ZGl0IGZpeFwiLFxyXG4gICAgXCJkZXBzOmNoZWNrXCI6IFwibnBtIG91dGRhdGVkXCIsXHJcbiAgICBcInBlcmY6bW9uaXRvclwiOiBcIm5vZGUgc2NyaXB0cy9wZXJmb3JtYW5jZS1tb25pdG9yLmpzXCIsXHJcbiAgICBcInBlcmY6Y2FjaGVcIjogXCJub2RlIHNjcmlwdHMvcGVyZm9ybWFuY2UtbW9uaXRvci5qcyAtLWNhY2hlXCIsXHJcbiAgICBcInBlcmY6YnVpbGRcIjogXCJub2RlIHNjcmlwdHMvcGVyZm9ybWFuY2UtbW9uaXRvci5qcyAtLWJ1aWxkXCIsXHJcbiAgICBcInByZXBhcmVcIjogXCJodXNreSBpbnN0YWxsXCIsXHJcbiAgICBcInJlbGVhc2VcIjogXCJzdGFuZGFyZC12ZXJzaW9uXCIsXHJcbiAgICBcImNvbW1pdFwiOiBcImdpdCBhZGQgLUEgJiYgY3pnICYmIGdpdCBwdXNoXCJcclxuICB9LFxyXG4gIFwiZGVwZW5kZW5jaWVzXCI6IHtcclxuICAgIFwiQGFudHYvZzJwbG90XCI6IFwiXjIuNC4zMlwiLFxyXG4gICAgXCJAYW50di94NlwiOiBcIl4yLjE4LjFcIixcclxuICAgIFwiQGFudHYveDYtcGx1Z2luLWNsaXBib2FyZFwiOiBcIjIuMS42XCIsXHJcbiAgICBcIkBhbnR2L3g2LXBsdWdpbi1kbmRcIjogXCIyLjEuMVwiLFxyXG4gICAgXCJAYW50di94Ni1wbHVnaW4tZXhwb3J0XCI6IFwiXjIuMS42XCIsXHJcbiAgICBcIkBhbnR2L3g2LXBsdWdpbi1oaXN0b3J5XCI6IFwiMi4yLjRcIixcclxuICAgIFwiQGFudHYveDYtcGx1Z2luLWtleWJvYXJkXCI6IFwiMi4yLjNcIixcclxuICAgIFwiQGFudHYveDYtcGx1Z2luLXNjcm9sbGVyXCI6IFwiMi4wLjEwXCIsXHJcbiAgICBcIkBhbnR2L3g2LXBsdWdpbi1zZWxlY3Rpb25cIjogXCIyLjIuMlwiLFxyXG4gICAgXCJAYW50di94Ni1wbHVnaW4tc25hcGxpbmVcIjogXCIyLjEuN1wiLFxyXG4gICAgXCJAYW50di94Ni1wbHVnaW4tdHJhbnNmb3JtXCI6IFwiXjIuMS44XCIsXHJcbiAgICBcIkBlbGVtZW50LXBsdXMvaWNvbnMtdnVlXCI6IFwiXjIuMy4xXCIsXHJcbiAgICBcIkBoaWdobGlnaHRqcy92dWUtcGx1Z2luXCI6IFwiXjIuMS4wXCIsXHJcbiAgICBcIkBpY29uaWZ5L3Z1ZVwiOiBcIl40LjEuMlwiLFxyXG4gICAgXCJAdnVldXNlL2NvcmVcIjogXCJeMTEuMC4zXCIsXHJcbiAgICBcImF4aW9zXCI6IFwiXjEuNy43XCIsXHJcbiAgICBcImNyeXB0by1qc1wiOiBcIl40LjEuMVwiLFxyXG4gICAgXCJkYXlqc1wiOiBcIl4xLjExLjEzXCIsXHJcbiAgICBcImRlY2ltYWwuanNcIjogXCJeMTAuNS4wXCIsXHJcbiAgICBcImRlZmF1bHQtcGFzc2l2ZS1ldmVudHNcIjogXCJeMi4wLjBcIixcclxuICAgIFwiZWNoYXJ0c1wiOiBcIl41LjUuMVwiLFxyXG4gICAgXCJlbGVtZW50LXBsdXNcIjogXCJeMi41LjZcIixcclxuICAgIFwiZW50aXRpZXNcIjogXCJeNC41LjBcIixcclxuICAgIFwiaGlnaGxpZ2h0LmpzXCI6IFwiXjExLjEwLjBcIixcclxuICAgIFwibWFya2Rvd24taXRcIjogXCJeMTQuMS4wXCIsXHJcbiAgICBcIm1kNVwiOiBcIl4yLjMuMFwiLFxyXG4gICAgXCJtaXR0XCI6IFwiXjMuMC4xXCIsXHJcbiAgICBcIm5wcm9ncmVzc1wiOiBcIl4wLjIuMFwiLFxyXG4gICAgXCJwaW5pYVwiOiBcIl4yLjIuMlwiLFxyXG4gICAgXCJwaW5pYS1wbHVnaW4tcGVyc2lzdGVkc3RhdGVcIjogXCJeMy4yLjFcIixcclxuICAgIFwicHJpbnQtanNcIjogXCJeMS42LjBcIixcclxuICAgIFwicXNcIjogXCJeNi4xMy4wXCIsXHJcbiAgICBcInNvcnRhYmxlanNcIjogXCJeMS4xNS4zXCIsXHJcbiAgICBcInNwbGl0LmpzXCI6IFwiXjEuNi41XCIsXHJcbiAgICBcInNwcmludGYtanNcIjogXCJeMS4xLjNcIixcclxuICAgIFwidi1jb250ZXh0bWVudVwiOiBcIl4zLjIuMFwiLFxyXG4gICAgXCJ2dWVcIjogXCJeMy41LjVcIixcclxuICAgIFwidnVlLWNyb3BwZXJcIjogXCJeMS4xLjFcIixcclxuICAgIFwidnVlLWkxOG5cIjogXCJeOS4xMy4xXCIsXHJcbiAgICBcInZ1ZS1yb3V0ZXJcIjogXCJeNC40LjVcIlxyXG4gIH0sXHJcbiAgXCJkZXZEZXBlbmRlbmNpZXNcIjoge1xyXG4gICAgXCJAY29tbWl0bGludC9jbGlcIjogXCJeMTkuNS4wXCIsXHJcbiAgICBcIkBjb21taXRsaW50L2NvbmZpZy1jb252ZW50aW9uYWxcIjogXCJeMTkuNS4wXCIsXHJcbiAgICBcIkBpY29uaWZ5L2pzb25cIjogXCJeMi4yLjI0N1wiLFxyXG4gICAgXCJAdHlwZXMvbWFya2Rvd24taXRcIjogXCJeMTQuMS4yXCIsXHJcbiAgICBcIkB0eXBlcy9tZDVcIjogXCJeMi4zLjVcIixcclxuICAgIFwiQHR5cGVzL25wcm9ncmVzc1wiOiBcIl4wLjIuM1wiLFxyXG4gICAgXCJAdHlwZXMvcXNcIjogXCJeNi45LjE1XCIsXHJcbiAgICBcIkB0eXBlcy9zbS1jcnlwdG9cIjogXCJeMC4zLjRcIixcclxuICAgIFwiQHR5cGVzL3NvcnRhYmxlanNcIjogXCJeMS4xNS44XCIsXHJcbiAgICBcIkB0eXBlcy91dWlkXCI6IFwiXjEwLjAuMFwiLFxyXG4gICAgXCJAdHlwZXNjcmlwdC1lc2xpbnQvZXNsaW50LXBsdWdpblwiOiBcIl43LjE0LjFcIixcclxuICAgIFwiQHR5cGVzY3JpcHQtZXNsaW50L3BhcnNlclwiOiBcIl43LjE0LjFcIixcclxuICAgIFwiQHZpdGVqcy9wbHVnaW4tdnVlXCI6IFwiXjUuMS4zXCIsXHJcbiAgICBcIkB2aXRlanMvcGx1Z2luLXZ1ZS1qc3hcIjogXCJeNC4wLjFcIixcclxuICAgIFwiYXV0b3ByZWZpeGVyXCI6IFwiXjEwLjQuMjBcIixcclxuICAgIFwiY29kZS1pbnNwZWN0b3ItcGx1Z2luXCI6IFwiXjAuMTYuMVwiLFxyXG4gICAgXCJjei1naXRcIjogXCJeMS45LjRcIixcclxuICAgIFwiY3pnXCI6IFwiXjEuOS40XCIsXHJcbiAgICBcImVzbGludFwiOiBcIl44LjU3LjBcIixcclxuICAgIFwiZXNsaW50LWNvbmZpZy1wcmV0dGllclwiOiBcIl45LjEuMFwiLFxyXG4gICAgXCJlc2xpbnQtcGx1Z2luLXByZXR0aWVyXCI6IFwiXjUuMS4zXCIsXHJcbiAgICBcImVzbGludC1wbHVnaW4tdnVlXCI6IFwiXjkuMjYuMFwiLFxyXG4gICAgXCJob3RrZXlzLWpzXCI6IFwiMy4xMy43XCIsXHJcbiAgICBcImh1c2t5XCI6IFwiXjkuMC4xMVwiLFxyXG4gICAgXCJsaW50LXN0YWdlZFwiOiBcIl4xNS4yLjEwXCIsXHJcbiAgICBcIm5haXZlLXVpXCI6IFwiXjIuMzkuMFwiLFxyXG4gICAgXCJwb3N0Y3NzXCI6IFwiXjguNC40NVwiLFxyXG4gICAgXCJwb3N0Y3NzLWh0bWxcIjogXCJeMS43LjBcIixcclxuICAgIFwicHJldHRpZXJcIjogXCJeMy4zLjNcIixcclxuICAgIFwicm9sbHVwLXBsdWdpbi12aXN1YWxpemVyXCI6IFwiXjUuMTQuMFwiLFxyXG4gICAgXCJzYXNzXCI6IFwiMS43NC4xXCIsXHJcbiAgICBcInNtLWNyeXB0b1wiOiBcIl4wLjMuMTNcIixcclxuICAgIFwic3RhbmRhcmQtdmVyc2lvblwiOiBcIl45LjUuMFwiLFxyXG4gICAgXCJzdHlsZWxpbnRcIjogXCJeMTYuOS4wXCIsXHJcbiAgICBcInN0eWxlbGludC1jb25maWctaHRtbFwiOiBcIl4xLjEuMFwiLFxyXG4gICAgXCJzdHlsZWxpbnQtY29uZmlnLXJlY2Vzcy1vcmRlclwiOiBcIl41LjEuMFwiLFxyXG4gICAgXCJzdHlsZWxpbnQtY29uZmlnLXJlY29tbWVuZGVkLXNjc3NcIjogXCJeMTQuMS4wXCIsXHJcbiAgICBcInN0eWxlbGludC1jb25maWctcmVjb21tZW5kZWQtdnVlXCI6IFwiXjEuNS4wXCIsXHJcbiAgICBcInN0eWxlbGludC1jb25maWctc3RhbmRhcmRcIjogXCJeMzYuMC4xXCIsXHJcbiAgICBcInN0eWxlbGludC1jb25maWctc3RhbmRhcmQtc2Nzc1wiOiBcIl4xMy4xLjBcIixcclxuICAgIFwidHlwZXNjcmlwdFwiOiBcIn41LjQuMFwiLFxyXG4gICAgXCJ1bm9jc3NcIjogXCJeMC42Mi4zXCIsXHJcbiAgICBcInVucGx1Z2luLWF1dG8taW1wb3J0XCI6IFwiXjAuMTguM1wiLFxyXG4gICAgXCJ1bnBsdWdpbi1pY29uc1wiOiBcIl4wLjE5LjNcIixcclxuICAgIFwidW5wbHVnaW4tdnVlLWNvbXBvbmVudHNcIjogXCJeMC4yNS4yXCIsXHJcbiAgICBcInVucGx1Z2luLXZ1ZS1zZXR1cC1leHRlbmQtcGx1c1wiOiBcIl4xLjAuMVwiLFxyXG4gICAgXCJ1dWlkXCI6IFwiXjguMy4yXCIsXHJcbiAgICBcInZpdGVcIjogXCJeNS40LjVcIixcclxuICAgIFwidml0ZS1wbHVnaW4tY29tcHJlc3Npb25cIjogXCJeMC41LjFcIixcclxuICAgIFwidml0ZS1wbHVnaW4tZXNsaW50XCI6IFwiXjEuOC4xXCIsXHJcbiAgICBcInZpdGUtcGx1Z2luLWh0bWxcIjogXCJeMy4yLjJcIixcclxuICAgIFwidml0ZS1wbHVnaW4tcHdhXCI6IFwiXjAuMjAuNVwiLFxyXG4gICAgXCJ2aXRlLXBsdWdpbi1zdmctaWNvbnNcIjogXCJeMi4wLjFcIixcclxuICAgIFwidml0ZS1wbHVnaW4tdnVlLWRldnRvb2xzXCI6IFwiXjcuMy41XCIsXHJcbiAgICBcInZ1ZS10c2NcIjogXCJeMi4xLjZcIlxyXG4gIH0sXHJcbiAgXCJvdmVycmlkZXNcIjoge30sXHJcbiAgXCJlbmdpbmVzXCI6IHtcclxuICAgIFwibm9kZVwiOiBcIj49MTYuMC4wXCJcclxuICB9LFxyXG4gIFwiYnJvd3NlcnNsaXN0XCI6IHtcclxuICAgIFwicHJvZHVjdGlvblwiOiBbXHJcbiAgICAgIFwiPiAxJVwiLFxyXG4gICAgICBcIm5vdCBkZWFkXCIsXHJcbiAgICAgIFwibm90IG9wX21pbmkgYWxsXCJcclxuICAgIF0sXHJcbiAgICBcImRldmVsb3BtZW50XCI6IFtcclxuICAgICAgXCJsYXN0IDEgY2hyb21lIHZlcnNpb25cIixcclxuICAgICAgXCJsYXN0IDEgZmlyZWZveCB2ZXJzaW9uXCIsXHJcbiAgICAgIFwibGFzdCAxIHNhZmFyaSB2ZXJzaW9uXCJcclxuICAgIF1cclxuICB9LFxyXG4gIFwiY29uZmlnXCI6IHtcclxuICAgIFwiY29tbWl0aXplblwiOiB7XHJcbiAgICAgIFwicGF0aFwiOiBcIm5vZGVfbW9kdWxlcy9jei1naXRcIlxyXG4gICAgfVxyXG4gIH1cclxufVxyXG4iLCAiY29uc3QgX192aXRlX2luamVjdGVkX29yaWdpbmFsX2Rpcm5hbWUgPSBcIkU6XFxcXFx1NURFNVx1NTE3N1x1OEY2Rlx1NEVGNlxcXFx2aXN1YWxkZWJ1Z1xcXFxmcm9udGVuZFxcXFxidWlsZFwiO2NvbnN0IF9fdml0ZV9pbmplY3RlZF9vcmlnaW5hbF9maWxlbmFtZSA9IFwiRTpcXFxcXHU1REU1XHU1MTc3XHU4RjZGXHU0RUY2XFxcXHZpc3VhbGRlYnVnXFxcXGZyb250ZW5kXFxcXGJ1aWxkXFxcXGVsZW1lbnQtcGx1cy1jb25maWcudHNcIjtjb25zdCBfX3ZpdGVfaW5qZWN0ZWRfb3JpZ2luYWxfaW1wb3J0X21ldGFfdXJsID0gXCJmaWxlOi8vL0U6LyVFNSVCNyVBNSVFNSU4NSVCNyVFOCVCRCVBRiVFNCVCQiVCNi92aXN1YWxkZWJ1Zy9mcm9udGVuZC9idWlsZC9lbGVtZW50LXBsdXMtY29uZmlnLnRzXCI7LyoqXG4gKiBFbGVtZW50IFBsdXMgXHU0RjE4XHU1MzE2XHU5MTREXHU3RjZFXG4gKiBcdTc1MjhcdTRFOEVcdTdCQTFcdTc0MDYgRWxlbWVudCBQbHVzIFx1N0VDNFx1NEVGNlx1NzY4NFx1OTg4NFx1Njc4NFx1NUVGQVx1NTQ4Q1x1NjgzN1x1NUYwRlx1NUJGQ1x1NTE2NVx1N0I1Nlx1NzU2NVxuICovXG5cbi8qKlxuICogRWxlbWVudCBQbHVzIFx1NjgzOFx1NUZDM1x1NEY5RFx1OEQ1Nlx1NTIxN1x1ODg2OFxuICogXHU4RkQ5XHU0RTlCXHU0RjlEXHU4RDU2XHU0RjFBXHU4OEFCXHU5ODg0XHU2Nzg0XHU1RUZBXHU0RUU1XHU2M0QwXHU1MzQ3XHU1RjAwXHU1M0QxXHU0RjUzXHU5QThDXG4gKi9cbmV4cG9ydCBjb25zdCBlbGVtZW50UGx1c0NvcmVJbmNsdWRlcyA9IFtcbiAgLy8gRWxlbWVudCBQbHVzIFx1NjgzOFx1NUZDM1x1NUU5M1xuICBcImVsZW1lbnQtcGx1c1wiLFxuICBcImVsZW1lbnQtcGx1cy9lc1wiLFxuICBcIkBlbGVtZW50LXBsdXMvaWNvbnMtdnVlXCJcbl07XG5cbi8qKlxuICogRWxlbWVudCBQbHVzIFx1N0VDNFx1NEVGNlx1NjgzN1x1NUYwRlx1OERFRlx1NUY4NFxuICogXHU3NTMxXHU0RThFXHU5ODc5XHU3NkVFXHU1REYyXHU1NzI4IG1haW4udHMgXHU0RTJEXHU1QkZDXHU1MTY1XHU1QjhDXHU2NTc0XHU2ODM3XHU1RjBGXHVGRjBDXHU4RkQ5XHU5MUNDXHU0RUM1XHU0RjVDXHU0RTNBXHU1M0MyXHU4MDAzXG4gKiBcdTU5ODJcdTY3OUNcdTk3MDBcdTg5ODFcdTYzMDlcdTk3MDBcdTVCRkNcdTUxNjVcdTY4MzdcdTVGMEZcdUZGMENcdTUzRUZcdTRFRTVcdTVDMDZcdThGRDlcdTRFOUJcdThERUZcdTVGODRcdTZERkJcdTUyQTBcdTUyMzAgb3B0aW1pemVEZXBzLmluY2x1ZGUgXHU0RTJEXG4gKi9cbmV4cG9ydCBjb25zdCBlbGVtZW50UGx1c1N0eWxlUGF0aHMgPSBbXG4gIC8vIFx1ODg2OFx1NTM1NVx1N0VDNFx1NEVGNlxuICBcImVsZW1lbnQtcGx1cy9lcy9jb21wb25lbnRzL2Zvcm0vc3R5bGUvY3NzXCIsXG4gIFwiZWxlbWVudC1wbHVzL2VzL2NvbXBvbmVudHMvZm9ybS1pdGVtL3N0eWxlL2Nzc1wiLFxuICBcImVsZW1lbnQtcGx1cy9lcy9jb21wb25lbnRzL2lucHV0L3N0eWxlL2Nzc1wiLFxuICBcImVsZW1lbnQtcGx1cy9lcy9jb21wb25lbnRzL2lucHV0LW51bWJlci9zdHlsZS9jc3NcIixcbiAgXCJlbGVtZW50LXBsdXMvZXMvY29tcG9uZW50cy9zZWxlY3Qvc3R5bGUvY3NzXCIsXG4gIFwiZWxlbWVudC1wbHVzL2VzL2NvbXBvbmVudHMvb3B0aW9uL3N0eWxlL2Nzc1wiLFxuICBcImVsZW1lbnQtcGx1cy9lcy9jb21wb25lbnRzL2NoZWNrYm94L3N0eWxlL2Nzc1wiLFxuICBcImVsZW1lbnQtcGx1cy9lcy9jb21wb25lbnRzL3JhZGlvL3N0eWxlL2Nzc1wiLFxuICBcImVsZW1lbnQtcGx1cy9lcy9jb21wb25lbnRzL3N3aXRjaC9zdHlsZS9jc3NcIixcbiAgXCJlbGVtZW50LXBsdXMvZXMvY29tcG9uZW50cy9zbGlkZXIvc3R5bGUvY3NzXCIsXG4gIFwiZWxlbWVudC1wbHVzL2VzL2NvbXBvbmVudHMvcmF0ZS9zdHlsZS9jc3NcIixcbiAgXCJlbGVtZW50LXBsdXMvZXMvY29tcG9uZW50cy9jb2xvci1waWNrZXIvc3R5bGUvY3NzXCIsXG4gIFwiZWxlbWVudC1wbHVzL2VzL2NvbXBvbmVudHMvZGF0ZS1waWNrZXIvc3R5bGUvY3NzXCIsXG4gIFwiZWxlbWVudC1wbHVzL2VzL2NvbXBvbmVudHMvdGltZS1waWNrZXIvc3R5bGUvY3NzXCIsXG4gIFwiZWxlbWVudC1wbHVzL2VzL2NvbXBvbmVudHMvdXBsb2FkL3N0eWxlL2Nzc1wiLFxuXG4gIC8vIFx1NjU3MFx1NjM2RVx1NUM1NVx1NzkzQVx1N0VDNFx1NEVGNlxuICBcImVsZW1lbnQtcGx1cy9lcy9jb21wb25lbnRzL3RhYmxlL3N0eWxlL2Nzc1wiLFxuICBcImVsZW1lbnQtcGx1cy9lcy9jb21wb25lbnRzL3RhYmxlLWNvbHVtbi9zdHlsZS9jc3NcIixcbiAgXCJlbGVtZW50LXBsdXMvZXMvY29tcG9uZW50cy9wYWdpbmF0aW9uL3N0eWxlL2Nzc1wiLFxuICBcImVsZW1lbnQtcGx1cy9lcy9jb21wb25lbnRzL3RhZy9zdHlsZS9jc3NcIixcbiAgXCJlbGVtZW50LXBsdXMvZXMvY29tcG9uZW50cy9wcm9ncmVzcy9zdHlsZS9jc3NcIixcbiAgXCJlbGVtZW50LXBsdXMvZXMvY29tcG9uZW50cy90cmVlL3N0eWxlL2Nzc1wiLFxuICBcImVsZW1lbnQtcGx1cy9lcy9jb21wb25lbnRzL2JhZGdlL3N0eWxlL2Nzc1wiLFxuICBcImVsZW1lbnQtcGx1cy9lcy9jb21wb25lbnRzL2NhcmQvc3R5bGUvY3NzXCIsXG4gIFwiZWxlbWVudC1wbHVzL2VzL2NvbXBvbmVudHMvY29sbGFwc2Uvc3R5bGUvY3NzXCIsXG4gIFwiZWxlbWVudC1wbHVzL2VzL2NvbXBvbmVudHMvdGltZWxpbmUvc3R5bGUvY3NzXCIsXG4gIFwiZWxlbWVudC1wbHVzL2VzL2NvbXBvbmVudHMvZGl2aWRlci9zdHlsZS9jc3NcIixcbiAgXCJlbGVtZW50LXBsdXMvZXMvY29tcG9uZW50cy9pbWFnZS9zdHlsZS9jc3NcIixcbiAgXCJlbGVtZW50LXBsdXMvZXMvY29tcG9uZW50cy9jYWxlbmRhci9zdHlsZS9jc3NcIixcblxuICAvLyBcdTVCRkNcdTgyMkFcdTdFQzRcdTRFRjZcbiAgXCJlbGVtZW50LXBsdXMvZXMvY29tcG9uZW50cy9tZW51L3N0eWxlL2Nzc1wiLFxuICBcImVsZW1lbnQtcGx1cy9lcy9jb21wb25lbnRzL3RhYnMvc3R5bGUvY3NzXCIsXG4gIFwiZWxlbWVudC1wbHVzL2VzL2NvbXBvbmVudHMvYnJlYWRjcnVtYi9zdHlsZS9jc3NcIixcbiAgXCJlbGVtZW50LXBsdXMvZXMvY29tcG9uZW50cy9icmVhZGNydW1iLWl0ZW0vc3R5bGUvY3NzXCIsXG4gIFwiZWxlbWVudC1wbHVzL2VzL2NvbXBvbmVudHMvcGFnZS1oZWFkZXIvc3R5bGUvY3NzXCIsXG4gIFwiZWxlbWVudC1wbHVzL2VzL2NvbXBvbmVudHMvc3RlcHMvc3R5bGUvY3NzXCIsXG4gIFwiZWxlbWVudC1wbHVzL2VzL2NvbXBvbmVudHMvZHJvcGRvd24vc3R5bGUvY3NzXCIsXG4gIFwiZWxlbWVudC1wbHVzL2VzL2NvbXBvbmVudHMvZHJvcGRvd24tbWVudS9zdHlsZS9jc3NcIixcbiAgXCJlbGVtZW50LXBsdXMvZXMvY29tcG9uZW50cy9kcm9wZG93bi1pdGVtL3N0eWxlL2Nzc1wiLFxuXG4gIC8vIFx1NTNDRFx1OTk4OFx1N0VDNFx1NEVGNlxuICBcImVsZW1lbnQtcGx1cy9lcy9jb21wb25lbnRzL2FsZXJ0L3N0eWxlL2Nzc1wiLFxuICBcImVsZW1lbnQtcGx1cy9lcy9jb21wb25lbnRzL2xvYWRpbmcvc3R5bGUvY3NzXCIsXG4gIFwiZWxlbWVudC1wbHVzL2VzL2NvbXBvbmVudHMvbWVzc2FnZS9zdHlsZS9jc3NcIixcbiAgXCJlbGVtZW50LXBsdXMvZXMvY29tcG9uZW50cy9tZXNzYWdlLWJveC9zdHlsZS9jc3NcIixcbiAgXCJlbGVtZW50LXBsdXMvZXMvY29tcG9uZW50cy9ub3RpZmljYXRpb24vc3R5bGUvY3NzXCIsXG4gIFwiZWxlbWVudC1wbHVzL2VzL2NvbXBvbmVudHMvZGlhbG9nL3N0eWxlL2Nzc1wiLFxuICBcImVsZW1lbnQtcGx1cy9lcy9jb21wb25lbnRzL3BvcG92ZXIvc3R5bGUvY3NzXCIsXG4gIFwiZWxlbWVudC1wbHVzL2VzL2NvbXBvbmVudHMvcG9wY29uZmlybS9zdHlsZS9jc3NcIixcbiAgXCJlbGVtZW50LXBsdXMvZXMvY29tcG9uZW50cy90b29sdGlwL3N0eWxlL2Nzc1wiLFxuICBcImVsZW1lbnQtcGx1cy9lcy9jb21wb25lbnRzL2RyYXdlci9zdHlsZS9jc3NcIixcbiAgXCJlbGVtZW50LXBsdXMvZXMvY29tcG9uZW50cy9yZXN1bHQvc3R5bGUvY3NzXCIsXG5cbiAgLy8gXHU1RTAzXHU1QzQwXHU3RUM0XHU0RUY2XG4gIFwiZWxlbWVudC1wbHVzL2VzL2NvbXBvbmVudHMvY29udGFpbmVyL3N0eWxlL2Nzc1wiLFxuICBcImVsZW1lbnQtcGx1cy9lcy9jb21wb25lbnRzL2hlYWRlci9zdHlsZS9jc3NcIixcbiAgXCJlbGVtZW50LXBsdXMvZXMvY29tcG9uZW50cy9hc2lkZS9zdHlsZS9jc3NcIixcbiAgXCJlbGVtZW50LXBsdXMvZXMvY29tcG9uZW50cy9tYWluL3N0eWxlL2Nzc1wiLFxuICBcImVsZW1lbnQtcGx1cy9lcy9jb21wb25lbnRzL2Zvb3Rlci9zdHlsZS9jc3NcIixcbiAgXCJlbGVtZW50LXBsdXMvZXMvY29tcG9uZW50cy9yb3cvc3R5bGUvY3NzXCIsXG4gIFwiZWxlbWVudC1wbHVzL2VzL2NvbXBvbmVudHMvY29sL3N0eWxlL2Nzc1wiLFxuICBcImVsZW1lbnQtcGx1cy9lcy9jb21wb25lbnRzL3NwYWNlL3N0eWxlL2Nzc1wiLFxuXG4gIC8vIFx1NTE3Nlx1NEVENlx1N0VDNFx1NEVGNlxuICBcImVsZW1lbnQtcGx1cy9lcy9jb21wb25lbnRzL2J1dHRvbi9zdHlsZS9jc3NcIixcbiAgXCJlbGVtZW50LXBsdXMvZXMvY29tcG9uZW50cy9idXR0b24tZ3JvdXAvc3R5bGUvY3NzXCIsXG4gIFwiZWxlbWVudC1wbHVzL2VzL2NvbXBvbmVudHMvbGluay9zdHlsZS9jc3NcIixcbiAgXCJlbGVtZW50LXBsdXMvZXMvY29tcG9uZW50cy90ZXh0L3N0eWxlL2Nzc1wiLFxuICBcImVsZW1lbnQtcGx1cy9lcy9jb21wb25lbnRzL3Njcm9sbGJhci9zdHlsZS9jc3NcIixcbiAgXCJlbGVtZW50LXBsdXMvZXMvY29tcG9uZW50cy9iYWNrdG9wL3N0eWxlL2Nzc1wiLFxuICBcImVsZW1lbnQtcGx1cy9lcy9jb21wb25lbnRzL2F2YXRhci9zdHlsZS9jc3NcIixcbiAgXCJlbGVtZW50LXBsdXMvZXMvY29tcG9uZW50cy9lbXB0eS9zdHlsZS9jc3NcIixcbiAgXCJlbGVtZW50LXBsdXMvZXMvY29tcG9uZW50cy9kZXNjcmlwdGlvbnMvc3R5bGUvY3NzXCIsXG4gIFwiZWxlbWVudC1wbHVzL2VzL2NvbXBvbmVudHMvZGVzY3JpcHRpb25zLWl0ZW0vc3R5bGUvY3NzXCIsXG4gIFwiZWxlbWVudC1wbHVzL2VzL2NvbXBvbmVudHMvc2tlbGV0b24vc3R5bGUvY3NzXCIsXG4gIFwiZWxlbWVudC1wbHVzL2VzL2NvbXBvbmVudHMvc2tlbGV0b24taXRlbS9zdHlsZS9jc3NcIixcbiAgXCJlbGVtZW50LXBsdXMvZXMvY29tcG9uZW50cy9hZmZpeC9zdHlsZS9jc3NcIixcbiAgXCJlbGVtZW50LXBsdXMvZXMvY29tcG9uZW50cy9hbmNob3Ivc3R5bGUvY3NzXCIsXG4gIFwiZWxlbWVudC1wbHVzL2VzL2NvbXBvbmVudHMvYW5jaG9yLWxpbmsvc3R5bGUvY3NzXCJcbl07XG5cbi8qKlxuICogRWxlbWVudCBQbHVzIFx1ODFFQVx1NTJBOFx1NUJGQ1x1NTE2NVx1ODlFM1x1Njc5MFx1NTY2OFx1OTE0RFx1N0Y2RVxuICovXG5leHBvcnQgY29uc3QgZWxlbWVudFBsdXNSZXNvbHZlckNvbmZpZyA9IHtcbiAgLy8gXHU3OTgxXHU3NTI4XHU2ODM3XHU1RjBGXHU4MUVBXHU1MkE4XHU1QkZDXHU1MTY1XHVGRjBDXHU1NkUwXHU0RTNBXHU1REYyXHU1NzI4IG1haW4udHMgXHU0RTJEXHU1QkZDXHU1MTY1XHU1QjhDXHU2NTc0XHU2ODM3XHU1RjBGXG4gIGltcG9ydFN0eWxlOiBmYWxzZSxcbiAgLy8gXHU2MzA3XHU1QjlBXHU4OUUzXHU2NzkwXHU3Njg0XHU1RTkzXHU1NDBEXG4gIHJlc29sdmVJY29uczogdHJ1ZSxcbiAgLy8gXHU3MjQ4XHU2NzJDXHU2OEMwXHU2RDRCXG4gIHZlcnNpb246IFwiXjIuNS42XCJcbn07XG5cbi8qKlxuICogXHU4M0I3XHU1M0Q2IEVsZW1lbnQgUGx1cyBcdTk4ODRcdTY3ODRcdTVFRkFcdTkxNERcdTdGNkVcbiAqIEBwYXJhbSB1c2VTdHlsZUluY2x1ZGVzIFx1NjYyRlx1NTQyNlx1NTMwNVx1NTQyQlx1NjgzN1x1NUYwRlx1OERFRlx1NUY4NFx1RkYwOFx1NUY1M1x1NEUwRFx1NEY3Rlx1NzUyOFx1NUI4Q1x1NjU3NFx1NjgzN1x1NUYwRlx1NUJGQ1x1NTE2NVx1NjVGNlx1RkYwOVxuICogQHJldHVybnMgXHU5ODg0XHU2Nzg0XHU1RUZBXHU1MzA1XHU1NDJCXHU1MjE3XHU4ODY4XG4gKi9cbmV4cG9ydCBmdW5jdGlvbiBnZXRFbGVtZW50UGx1c0luY2x1ZGVzKHVzZVN0eWxlSW5jbHVkZXMgPSBmYWxzZSk6IHN0cmluZ1tdIHtcbiAgaWYgKHVzZVN0eWxlSW5jbHVkZXMpIHtcbiAgICByZXR1cm4gWy4uLmVsZW1lbnRQbHVzQ29yZUluY2x1ZGVzLCAuLi5lbGVtZW50UGx1c1N0eWxlUGF0aHNdO1xuICB9XG4gIHJldHVybiBlbGVtZW50UGx1c0NvcmVJbmNsdWRlcztcbn1cblxuLyoqXG4gKiBcdTY4QzBcdTY3RTVcdTY2MkZcdTU0MjZcdTRFM0EgRWxlbWVudCBQbHVzIFx1NzZGOFx1NTE3M1x1NkEyMVx1NTc1N1xuICogQHBhcmFtIGlkIFx1NkEyMVx1NTc1N0lEXG4gKiBAcmV0dXJucyBcdTY2MkZcdTU0MjZcdTRFM0EgRWxlbWVudCBQbHVzIFx1NkEyMVx1NTc1N1xuICovXG5leHBvcnQgZnVuY3Rpb24gaXNFbGVtZW50UGx1c01vZHVsZShpZDogc3RyaW5nKTogYm9vbGVhbiB7XG4gIHJldHVybiBpZC5pbmNsdWRlcyhcImVsZW1lbnQtcGx1c1wiKSB8fCBpZC5pbmNsdWRlcyhcIkBlbGVtZW50LXBsdXNcIik7XG59XG5cbi8qKlxuICogXHU4M0I3XHU1M0Q2IEVsZW1lbnQgUGx1cyBcdTZBMjFcdTU3NTdcdTc2ODQgY2h1bmsgXHU1NDBEXHU3OUYwXG4gKiBAcGFyYW0gaWQgXHU2QTIxXHU1NzU3SURcbiAqIEByZXR1cm5zIGNodW5rIFx1NTQwRFx1NzlGMFxuICovXG5leHBvcnQgZnVuY3Rpb24gZ2V0RWxlbWVudFBsdXNDaHVua05hbWUoaWQ6IHN0cmluZyk6IHN0cmluZyB7XG4gIGlmIChpZC5pbmNsdWRlcyhcIkBlbGVtZW50LXBsdXMvaWNvbnMtdnVlXCIpKSB7XG4gICAgcmV0dXJuIFwiZWxlbWVudC1pY29uc1wiO1xuICB9XG4gIGlmIChpZC5pbmNsdWRlcyhcImVsZW1lbnQtcGx1cy9lcy9jb21wb25lbnRzXCIpKSB7XG4gICAgcmV0dXJuIFwiZWxlbWVudC1jb21wb25lbnRzXCI7XG4gIH1cbiAgaWYgKGlkLmluY2x1ZGVzKFwiZWxlbWVudC1wbHVzXCIpKSB7XG4gICAgcmV0dXJuIFwiZWxlbWVudC1jb3JlXCI7XG4gIH1cbiAgcmV0dXJuIFwiZWxlbWVudC12ZW5kb3JcIjtcbn1cblxuLyoqXG4gKiBFbGVtZW50IFBsdXMgXHU0RjE4XHU1MzE2XHU1RUZBXHU4QkFFXG4gKi9cbmV4cG9ydCBjb25zdCBlbGVtZW50UGx1c09wdGltaXphdGlvblRpcHMgPSB7XG4gIC8vIFx1NjgzN1x1NUYwRlx1NUJGQ1x1NTE2NVx1N0I1Nlx1NzU2NVxuICBzdHlsZVN0cmF0ZWd5OiB7XG4gICAgZnVsbDoge1xuICAgICAgZGVzY3JpcHRpb246IFwiXHU1QkZDXHU1MTY1XHU1QjhDXHU2NTc0XHU2ODM3XHU1RjBGXHU2NTg3XHU0RUY2XHVGRjA4XHU2M0E4XHU4MzUwXHVGRjA5XCIsXG4gICAgICBpbXBvcnQ6ICdpbXBvcnQgXCJlbGVtZW50LXBsdXMvZGlzdC9pbmRleC5jc3NcIicsXG4gICAgICBwcm9zOiBbXCJcdTY1RTBcdTk3MDBcdTkxNERcdTdGNkVcIiwgXCJcdTkwN0ZcdTUxNERcdTY4MzdcdTVGMEZcdTRFMjJcdTU5MzFcIiwgXCJcdTVGMDBcdTUzRDFcdTRGNTNcdTlBOENcdTU5N0RcIl0sXG4gICAgICBjb25zOiBbXCJcdTUzMDVcdTRGNTNcdTc5RUZcdTdBMERcdTU5MjdcIiwgXCJcdTY1RTBcdTZDRDV0cmVlLXNoYWtpbmdcdTY4MzdcdTVGMEZcIl1cbiAgICB9LFxuICAgIG9uRGVtYW5kOiB7XG4gICAgICBkZXNjcmlwdGlvbjogXCJcdTYzMDlcdTk3MDBcdTVCRkNcdTUxNjVcdTY4MzdcdTVGMEZcIixcbiAgICAgIGltcG9ydDogXCJcdTkwMUFcdThGQzcgdW5wbHVnaW4tZWxlbWVudC1wbHVzIFx1ODFFQVx1NTJBOFx1NUJGQ1x1NTE2NVwiLFxuICAgICAgcHJvczogW1wiXHU1MzA1XHU0RjUzXHU3OUVGXHU1QzBGXCIsIFwiXHU2NTJGXHU2MzAxdHJlZS1zaGFraW5nXCJdLFxuICAgICAgY29uczogW1wiXHU5MTREXHU3RjZFXHU1OTBEXHU2NzQyXCIsIFwiXHU1M0VGXHU4MEZEXHU5MDU3XHU2RjBGXHU2ODM3XHU1RjBGXCJdXG4gICAgfVxuICB9LFxuXG4gIC8vIFx1N0VDNFx1NEVGNlx1NUJGQ1x1NTE2NVx1N0I1Nlx1NzU2NVxuICBjb21wb25lbnRTdHJhdGVneToge1xuICAgIGF1dG86IHtcbiAgICAgIGRlc2NyaXB0aW9uOiBcIlx1ODFFQVx1NTJBOFx1NUJGQ1x1NTE2NVx1N0VDNFx1NEVGNlx1RkYwOFx1NjNBOFx1ODM1MFx1RkYwOVwiLFxuICAgICAgY29uZmlnOiBcInVucGx1Z2luLXZ1ZS1jb21wb25lbnRzICsgRWxlbWVudFBsdXNSZXNvbHZlclwiLFxuICAgICAgcHJvczogW1wiXHU2NUUwXHU5NzAwXHU2MjRCXHU1MkE4XHU1QkZDXHU1MTY1XCIsIFwiXHU2NTJGXHU2MzAxdHJlZS1zaGFraW5nXCIsIFwiXHU1RjAwXHU1M0QxXHU0RjUzXHU5QThDXHU1OTdEXCJdLFxuICAgICAgY29uczogW1wiXHU5NzAwXHU4OTgxXHU5MTREXHU3RjZFXHU4OUUzXHU2NzkwXHU1NjY4XCJdXG4gICAgfSxcbiAgICBtYW51YWw6IHtcbiAgICAgIGRlc2NyaXB0aW9uOiBcIlx1NjI0Qlx1NTJBOFx1NUJGQ1x1NTE2NVx1N0VDNFx1NEVGNlwiLFxuICAgICAgaW1wb3J0OiAnaW1wb3J0IHsgRWxCdXR0b24gfSBmcm9tIFwiZWxlbWVudC1wbHVzXCInLFxuICAgICAgcHJvczogW1wiXHU1QjhDXHU1MTY4XHU1M0VGXHU2M0E3XCIsIFwiXHU1MzA1XHU0RjUzXHU3OUVGXHU2NzAwXHU1QzBGXCJdLFxuICAgICAgY29uczogW1wiXHU1RjAwXHU1M0QxXHU2NTQ4XHU3Mzg3XHU0RjRFXCIsIFwiXHU1QkI5XHU2NjEzXHU5MDU3XHU2RjBGXCJdXG4gICAgfVxuICB9XG59O1xuXG5leHBvcnQgZGVmYXVsdCB7XG4gIGVsZW1lbnRQbHVzQ29yZUluY2x1ZGVzLFxuICBlbGVtZW50UGx1c1N0eWxlUGF0aHMsXG4gIGVsZW1lbnRQbHVzUmVzb2x2ZXJDb25maWcsXG4gIGdldEVsZW1lbnRQbHVzSW5jbHVkZXMsXG4gIGlzRWxlbWVudFBsdXNNb2R1bGUsXG4gIGdldEVsZW1lbnRQbHVzQ2h1bmtOYW1lLFxuICBlbGVtZW50UGx1c09wdGltaXphdGlvblRpcHNcbn07XG4iXSwKICAibWFwcGluZ3MiOiAiO0FBQThTLFNBQVMsY0FBYyxlQUFzQztBQUMzVyxTQUFTLFdBQUFBLGdCQUFlOzs7QUNxQmpCLFNBQVMsV0FBVyxTQUE4QjtBQUN2RCxRQUFNLE1BQVcsQ0FBQztBQUVsQixhQUFXLFdBQVcsT0FBTyxLQUFLLE9BQU8sR0FBRztBQUMxQyxRQUFJLFdBQVcsUUFBUSxPQUFPLEVBQUUsUUFBUSxRQUFRLElBQUk7QUFDcEQsZUFBVyxhQUFhLFNBQVMsT0FBTyxhQUFhLFVBQVUsUUFBUTtBQUN2RSxRQUFJLFlBQVksWUFBYSxZQUFXLE9BQU8sUUFBUTtBQUN2RCxRQUFJLFlBQVksY0FBYztBQUM1QixVQUFJO0FBQ0YsbUJBQVcsS0FBSyxNQUFNLFFBQVE7QUFBQSxNQUNoQyxTQUFTLE9BQU87QUFBQSxNQUFDO0FBQUEsSUFDbkI7QUFDQSxRQUFJLE9BQU8sSUFBSTtBQUFBLEVBQ2pCO0FBQ0EsU0FBTztBQUNUOzs7QUN6Qk8sU0FBUyxZQUFZLE9BQWtCLENBQUMsR0FBRztBQUNoRCxRQUFNLE1BQXVCLENBQUM7QUFDOUIsYUFBVyxDQUFDLFFBQVEsTUFBTSxLQUFLLE1BQU07QUFDbkMsVUFBTSxVQUFVO0FBQ2hCLFVBQU0sVUFBVSxRQUFRLEtBQUssTUFBTTtBQUduQyxRQUFJLE1BQU0sSUFBSTtBQUFBLE1BQ1o7QUFBQSxNQUNBLGNBQWM7QUFBQSxNQUNkLElBQUk7QUFBQSxNQUNKLFNBQVMsVUFBUSxLQUFLLFFBQVEsSUFBSSxPQUFPLElBQUksTUFBTSxFQUFFLEdBQUcsRUFBRTtBQUFBO0FBQUEsTUFFMUQsR0FBSSxVQUFVLEVBQUUsUUFBUSxNQUFNLElBQUksQ0FBQztBQUFBLElBQ3JDO0FBQUEsRUFDRjtBQUNBLFNBQU87QUFDVDs7O0FDN0IwVCxTQUFTLGVBQWU7QUFFbFYsU0FBUyxlQUFlO0FBQ3hCLFNBQVMsa0JBQWtCO0FBQzNCLFNBQVMsNEJBQTRCO0FBQ3JDLFNBQVMsd0JBQXdCO0FBQ2pDLE9BQU8sU0FBUztBQUNoQixPQUFPLFlBQVk7QUFDbkIsT0FBTyxrQkFBa0I7QUFDekIsT0FBTyxxQkFBcUI7QUFDNUIsT0FBTyxvQkFBb0I7QUFDM0IsT0FBTyxZQUFZO0FBQ25CLE9BQU8sZ0JBQWdCO0FBQ3ZCLFNBQVMsMkJBQTJCO0FBQ3BDLE9BQU8sZ0JBQWdCO0FBQ3ZCLE9BQU8sV0FBVztBQUNsQixPQUFPLG1CQUFtQjtBQUMxQixPQUFPLGtCQUFrQjtBQUN6QixTQUFTLDJCQUEyQjtBQUs3QixJQUFNLG9CQUFvQixDQUFDLFlBQXdEO0FBQ3hGLFFBQU0sRUFBRSxxQkFBcUIsYUFBYSxlQUFlLFVBQVUsbUJBQW1CLElBQUk7QUFFMUYsU0FBTztBQUFBLElBQ0wsSUFBSTtBQUFBO0FBQUEsSUFFSixpQkFBaUIsYUFBYSxFQUFFLGNBQWMsT0FBTyxDQUFDO0FBQUE7QUFBQSxJQUV0RCxPQUFPO0FBQUE7QUFBQSxJQUVQLGFBQWE7QUFBQTtBQUFBLElBRWIsZUFBZSxDQUFDLENBQUM7QUFBQTtBQUFBLElBRWpCLGtCQUFrQixPQUFPO0FBQUE7QUFBQSxJQUV6QixpQkFBaUI7QUFBQSxNQUNmLFFBQVE7QUFBQSxNQUNSLFFBQVE7QUFBQSxRQUNOLE1BQU0sRUFBRSxPQUFPLG9CQUFvQjtBQUFBLE1BQ3JDO0FBQUEsSUFDRixDQUFDO0FBQUE7QUFBQSxJQUVELHFCQUFxQjtBQUFBLE1BQ25CLFVBQVUsQ0FBQyxRQUFRLFFBQVEsSUFBSSxHQUFHLGdCQUFnQixDQUFDO0FBQUEsTUFDbkQsVUFBVTtBQUFBLElBQ1osQ0FBQztBQUFBO0FBQUEsSUFFRCxZQUFZLGNBQWMsT0FBTztBQUFBO0FBQUEsSUFFakMsZUFBZ0IsV0FBVyxFQUFFLFVBQVUsY0FBYyxVQUFVLE1BQU0sWUFBWSxLQUFLLENBQUM7QUFBQTtBQUFBLElBRXZGLHNCQUNFLG9CQUFvQjtBQUFBLE1BQ2xCLFNBQVM7QUFBQSxJQUNYLENBQUM7QUFBQTtBQUFBLElBRUgsV0FBVztBQUFBLE1BQ1QsU0FBUyxDQUFDLE9BQU8sWUFBWTtBQUFBO0FBQUE7QUFBQSxNQUc3QixXQUFXO0FBQUEsUUFDVCxvQkFBb0I7QUFBQTtBQUFBLFVBRWxCLGFBQWE7QUFBQSxRQUNmLENBQUM7QUFBQTtBQUFBO0FBQUEsUUFJRCxjQUFjO0FBQUEsVUFDWixRQUFRO0FBQUEsUUFDVixDQUFDO0FBQUEsTUFDSDtBQUFBLE1BQ0EsS0FBSztBQUFBO0FBQUEsSUFDUCxDQUFDO0FBQUEsSUFDRCxXQUFXO0FBQUEsTUFDVCxNQUFNLENBQUMsZ0JBQWdCO0FBQUE7QUFBQSxNQUN2QixXQUFXO0FBQUE7QUFBQTtBQUFBLFFBR1QsY0FBYztBQUFBLFVBQ1osb0JBQW9CLENBQUMsSUFBSTtBQUFBO0FBQUEsUUFDM0IsQ0FBQztBQUFBO0FBQUE7QUFBQSxRQUdELG9CQUFvQjtBQUFBO0FBQUEsVUFFbEIsYUFBYTtBQUFBO0FBQUEsVUFFYixjQUFjO0FBQUEsUUFDaEIsQ0FBQztBQUFBLE1BQ0g7QUFBQTtBQUFBLE1BRUEsS0FBSztBQUFBLElBQ1AsQ0FBQztBQUFBLElBQ0QsTUFBTTtBQUFBLE1BQ0osVUFBVTtBQUFBLE1BQ1YsYUFBYTtBQUFBLElBQ2YsQ0FBQztBQUFBLElBRUQsT0FBTztBQUFBO0FBQUEsRUFDVDtBQUNGO0FBTUEsSUFBTSxvQkFBb0IsQ0FBQyxZQUFvRDtBQUM3RSxRQUFNLEVBQUUsc0JBQXNCLFFBQVEsdUNBQXVDLElBQUk7QUFDakYsUUFBTSxlQUFlLG9CQUFvQixNQUFNLEdBQUc7QUFDbEQsUUFBTSxVQUEwQixDQUFDO0FBQ2pDLE1BQUksYUFBYSxTQUFTLE1BQU0sR0FBRztBQUNqQyxZQUFRO0FBQUEsTUFDTixnQkFBZ0I7QUFBQSxRQUNkLEtBQUs7QUFBQSxRQUNMLFdBQVc7QUFBQSxRQUNYLGtCQUFrQjtBQUFBLE1BQ3BCLENBQUM7QUFBQSxJQUNIO0FBQUEsRUFDRjtBQUNBLE1BQUksYUFBYSxTQUFTLFFBQVEsR0FBRztBQUNuQyxZQUFRO0FBQUEsTUFDTixnQkFBZ0I7QUFBQSxRQUNkLEtBQUs7QUFBQSxRQUNMLFdBQVc7QUFBQSxRQUNYLGtCQUFrQjtBQUFBLE1BQ3BCLENBQUM7QUFBQSxJQUNIO0FBQUEsRUFDRjtBQUNBLFNBQU87QUFDVDtBQU1BLElBQU0sZ0JBQWdCLENBQUMsWUFBb0Q7QUFDekUsUUFBTSxFQUFFLG9CQUFvQixJQUFJO0FBQ2hDLFNBQU8sUUFBUTtBQUFBLElBQ2IsY0FBYztBQUFBLElBQ2QsU0FBUztBQUFBO0FBQUEsTUFFUCwrQkFBK0IsSUFBSSxPQUFPO0FBQUEsSUFDNUM7QUFBQSxJQUNBLFVBQVU7QUFBQSxNQUNSLE1BQU07QUFBQSxNQUNOLFlBQVk7QUFBQSxNQUNaLGFBQWE7QUFBQSxNQUNiLE9BQU87QUFBQSxRQUNMO0FBQUEsVUFDRSxLQUFLO0FBQUEsVUFDTCxPQUFPO0FBQUEsVUFDUCxNQUFNO0FBQUEsUUFDUjtBQUFBLFFBQ0E7QUFBQSxVQUNFLEtBQUs7QUFBQSxVQUNMLE9BQU87QUFBQSxVQUNQLE1BQU07QUFBQSxRQUNSO0FBQUEsUUFDQTtBQUFBLFVBQ0UsS0FBSztBQUFBLFVBQ0wsT0FBTztBQUFBLFVBQ1AsTUFBTTtBQUFBLFVBQ04sU0FBUztBQUFBLFFBQ1g7QUFBQSxNQUNGO0FBQUEsSUFDRjtBQUFBLEVBQ0YsQ0FBQztBQUNIOzs7QUh2S0EsU0FBUyxjQUFBQyxtQkFBa0I7OztBSUwzQjtBQUFBLEVBQ0UsTUFBUTtBQUFBLEVBQ1IsU0FBVztBQUFBLEVBQ1gsTUFBUTtBQUFBLEVBQ1IsYUFBZTtBQUFBLEVBQ2YsU0FBVztBQUFBLEVBQ1gsU0FBVztBQUFBLElBQ1QsS0FBTztBQUFBLElBQ1AsYUFBYTtBQUFBLElBQ2IsT0FBUztBQUFBLElBQ1QsYUFBYTtBQUFBLElBQ2IsY0FBYztBQUFBLElBQ2QsYUFBYTtBQUFBLElBQ2IsaUJBQWlCO0FBQUEsSUFDakIsY0FBYztBQUFBLElBQ2QsU0FBVztBQUFBLElBQ1gsZUFBZTtBQUFBLElBQ2YsaUJBQWlCO0FBQUEsSUFDakIsa0JBQWtCO0FBQUEsSUFDbEIsb0JBQW9CO0FBQUEsSUFDcEIsZUFBZTtBQUFBLElBQ2YsbUJBQW1CO0FBQUEsSUFDbkIscUJBQXFCO0FBQUEsSUFDckIsbUJBQW1CO0FBQUEsSUFDbkIsZUFBZTtBQUFBLElBQ2YsY0FBYztBQUFBLElBQ2QsZ0JBQWdCO0FBQUEsSUFDaEIsY0FBYztBQUFBLElBQ2QsY0FBYztBQUFBLElBQ2QsU0FBVztBQUFBLElBQ1gsU0FBVztBQUFBLElBQ1gsUUFBVTtBQUFBLEVBQ1o7QUFBQSxFQUNBLGNBQWdCO0FBQUEsSUFDZCxnQkFBZ0I7QUFBQSxJQUNoQixZQUFZO0FBQUEsSUFDWiw2QkFBNkI7QUFBQSxJQUM3Qix1QkFBdUI7QUFBQSxJQUN2QiwwQkFBMEI7QUFBQSxJQUMxQiwyQkFBMkI7QUFBQSxJQUMzQiw0QkFBNEI7QUFBQSxJQUM1Qiw0QkFBNEI7QUFBQSxJQUM1Qiw2QkFBNkI7QUFBQSxJQUM3Qiw0QkFBNEI7QUFBQSxJQUM1Qiw2QkFBNkI7QUFBQSxJQUM3QiwyQkFBMkI7QUFBQSxJQUMzQiwyQkFBMkI7QUFBQSxJQUMzQixnQkFBZ0I7QUFBQSxJQUNoQixnQkFBZ0I7QUFBQSxJQUNoQixPQUFTO0FBQUEsSUFDVCxhQUFhO0FBQUEsSUFDYixPQUFTO0FBQUEsSUFDVCxjQUFjO0FBQUEsSUFDZCwwQkFBMEI7QUFBQSxJQUMxQixTQUFXO0FBQUEsSUFDWCxnQkFBZ0I7QUFBQSxJQUNoQixVQUFZO0FBQUEsSUFDWixnQkFBZ0I7QUFBQSxJQUNoQixlQUFlO0FBQUEsSUFDZixLQUFPO0FBQUEsSUFDUCxNQUFRO0FBQUEsSUFDUixXQUFhO0FBQUEsSUFDYixPQUFTO0FBQUEsSUFDVCwrQkFBK0I7QUFBQSxJQUMvQixZQUFZO0FBQUEsSUFDWixJQUFNO0FBQUEsSUFDTixZQUFjO0FBQUEsSUFDZCxZQUFZO0FBQUEsSUFDWixjQUFjO0FBQUEsSUFDZCxpQkFBaUI7QUFBQSxJQUNqQixLQUFPO0FBQUEsSUFDUCxlQUFlO0FBQUEsSUFDZixZQUFZO0FBQUEsSUFDWixjQUFjO0FBQUEsRUFDaEI7QUFBQSxFQUNBLGlCQUFtQjtBQUFBLElBQ2pCLG1CQUFtQjtBQUFBLElBQ25CLG1DQUFtQztBQUFBLElBQ25DLGlCQUFpQjtBQUFBLElBQ2pCLHNCQUFzQjtBQUFBLElBQ3RCLGNBQWM7QUFBQSxJQUNkLG9CQUFvQjtBQUFBLElBQ3BCLGFBQWE7QUFBQSxJQUNiLG9CQUFvQjtBQUFBLElBQ3BCLHFCQUFxQjtBQUFBLElBQ3JCLGVBQWU7QUFBQSxJQUNmLG9DQUFvQztBQUFBLElBQ3BDLDZCQUE2QjtBQUFBLElBQzdCLHNCQUFzQjtBQUFBLElBQ3RCLDBCQUEwQjtBQUFBLElBQzFCLGNBQWdCO0FBQUEsSUFDaEIseUJBQXlCO0FBQUEsSUFDekIsVUFBVTtBQUFBLElBQ1YsS0FBTztBQUFBLElBQ1AsUUFBVTtBQUFBLElBQ1YsMEJBQTBCO0FBQUEsSUFDMUIsMEJBQTBCO0FBQUEsSUFDMUIscUJBQXFCO0FBQUEsSUFDckIsY0FBYztBQUFBLElBQ2QsT0FBUztBQUFBLElBQ1QsZUFBZTtBQUFBLElBQ2YsWUFBWTtBQUFBLElBQ1osU0FBVztBQUFBLElBQ1gsZ0JBQWdCO0FBQUEsSUFDaEIsVUFBWTtBQUFBLElBQ1osNEJBQTRCO0FBQUEsSUFDNUIsTUFBUTtBQUFBLElBQ1IsYUFBYTtBQUFBLElBQ2Isb0JBQW9CO0FBQUEsSUFDcEIsV0FBYTtBQUFBLElBQ2IseUJBQXlCO0FBQUEsSUFDekIsaUNBQWlDO0FBQUEsSUFDakMscUNBQXFDO0FBQUEsSUFDckMsb0NBQW9DO0FBQUEsSUFDcEMsNkJBQTZCO0FBQUEsSUFDN0Isa0NBQWtDO0FBQUEsSUFDbEMsWUFBYztBQUFBLElBQ2QsUUFBVTtBQUFBLElBQ1Ysd0JBQXdCO0FBQUEsSUFDeEIsa0JBQWtCO0FBQUEsSUFDbEIsMkJBQTJCO0FBQUEsSUFDM0Isa0NBQWtDO0FBQUEsSUFDbEMsTUFBUTtBQUFBLElBQ1IsTUFBUTtBQUFBLElBQ1IsMkJBQTJCO0FBQUEsSUFDM0Isc0JBQXNCO0FBQUEsSUFDdEIsb0JBQW9CO0FBQUEsSUFDcEIsbUJBQW1CO0FBQUEsSUFDbkIseUJBQXlCO0FBQUEsSUFDekIsNEJBQTRCO0FBQUEsSUFDNUIsV0FBVztBQUFBLEVBQ2I7QUFBQSxFQUNBLFdBQWEsQ0FBQztBQUFBLEVBQ2QsU0FBVztBQUFBLElBQ1QsTUFBUTtBQUFBLEVBQ1Y7QUFBQSxFQUNBLGNBQWdCO0FBQUEsSUFDZCxZQUFjO0FBQUEsTUFDWjtBQUFBLE1BQ0E7QUFBQSxNQUNBO0FBQUEsSUFDRjtBQUFBLElBQ0EsYUFBZTtBQUFBLE1BQ2I7QUFBQSxNQUNBO0FBQUEsTUFDQTtBQUFBLElBQ0Y7QUFBQSxFQUNGO0FBQUEsRUFDQSxRQUFVO0FBQUEsSUFDUixZQUFjO0FBQUEsTUFDWixNQUFRO0FBQUEsSUFDVjtBQUFBLEVBQ0Y7QUFDRjs7O0FKbEpBLE9BQU8sV0FBVzs7O0FLRVgsSUFBTSwwQkFBMEI7QUFBQTtBQUFBLEVBRXJDO0FBQUEsRUFDQTtBQUFBLEVBQ0E7QUFDRjtBQU9PLElBQU0sd0JBQXdCO0FBQUE7QUFBQSxFQUVuQztBQUFBLEVBQ0E7QUFBQSxFQUNBO0FBQUEsRUFDQTtBQUFBLEVBQ0E7QUFBQSxFQUNBO0FBQUEsRUFDQTtBQUFBLEVBQ0E7QUFBQSxFQUNBO0FBQUEsRUFDQTtBQUFBLEVBQ0E7QUFBQSxFQUNBO0FBQUEsRUFDQTtBQUFBLEVBQ0E7QUFBQSxFQUNBO0FBQUE7QUFBQSxFQUdBO0FBQUEsRUFDQTtBQUFBLEVBQ0E7QUFBQSxFQUNBO0FBQUEsRUFDQTtBQUFBLEVBQ0E7QUFBQSxFQUNBO0FBQUEsRUFDQTtBQUFBLEVBQ0E7QUFBQSxFQUNBO0FBQUEsRUFDQTtBQUFBLEVBQ0E7QUFBQSxFQUNBO0FBQUE7QUFBQSxFQUdBO0FBQUEsRUFDQTtBQUFBLEVBQ0E7QUFBQSxFQUNBO0FBQUEsRUFDQTtBQUFBLEVBQ0E7QUFBQSxFQUNBO0FBQUEsRUFDQTtBQUFBLEVBQ0E7QUFBQTtBQUFBLEVBR0E7QUFBQSxFQUNBO0FBQUEsRUFDQTtBQUFBLEVBQ0E7QUFBQSxFQUNBO0FBQUEsRUFDQTtBQUFBLEVBQ0E7QUFBQSxFQUNBO0FBQUEsRUFDQTtBQUFBLEVBQ0E7QUFBQSxFQUNBO0FBQUE7QUFBQSxFQUdBO0FBQUEsRUFDQTtBQUFBLEVBQ0E7QUFBQSxFQUNBO0FBQUEsRUFDQTtBQUFBLEVBQ0E7QUFBQSxFQUNBO0FBQUEsRUFDQTtBQUFBO0FBQUEsRUFHQTtBQUFBLEVBQ0E7QUFBQSxFQUNBO0FBQUEsRUFDQTtBQUFBLEVBQ0E7QUFBQSxFQUNBO0FBQUEsRUFDQTtBQUFBLEVBQ0E7QUFBQSxFQUNBO0FBQUEsRUFDQTtBQUFBLEVBQ0E7QUFBQSxFQUNBO0FBQUEsRUFDQTtBQUFBLEVBQ0E7QUFBQSxFQUNBO0FBQ0Y7QUFtQk8sU0FBUyx1QkFBdUIsbUJBQW1CLE9BQWlCO0FBQ3pFLE1BQUksa0JBQWtCO0FBQ3BCLFdBQU8sQ0FBQyxHQUFHLHlCQUF5QixHQUFHLHFCQUFxQjtBQUFBLEVBQzlEO0FBQ0EsU0FBTztBQUNUO0FBT08sU0FBUyxvQkFBb0IsSUFBcUI7QUFDdkQsU0FBTyxHQUFHLFNBQVMsY0FBYyxLQUFLLEdBQUcsU0FBUyxlQUFlO0FBQ25FO0FBT08sU0FBUyx3QkFBd0IsSUFBb0I7QUFDMUQsTUFBSSxHQUFHLFNBQVMseUJBQXlCLEdBQUc7QUFDMUMsV0FBTztBQUFBLEVBQ1Q7QUFDQSxNQUFJLEdBQUcsU0FBUyw0QkFBNEIsR0FBRztBQUM3QyxXQUFPO0FBQUEsRUFDVDtBQUNBLE1BQUksR0FBRyxTQUFTLGNBQWMsR0FBRztBQUMvQixXQUFPO0FBQUEsRUFDVDtBQUNBLFNBQU87QUFDVDs7O0FMM0pBLElBQU0sbUNBQW1DO0FBVXpDLElBQU0sRUFBRSxjQUFjLGlCQUFpQixLQUFLLElBQUk7QUFDaEQsSUFBTSxlQUFlO0FBQUEsRUFDbkIsS0FBSyxFQUFFLGNBQWMsaUJBQWlCLEtBQUs7QUFBQSxFQUMzQyxlQUFlLE1BQU0sRUFBRSxPQUFPLHFCQUFxQjtBQUNyRDtBQUdBLElBQU8sc0JBQVEsYUFBYSxDQUFDLEVBQUUsS0FBSyxNQUE2QjtBQUMvRCxRQUFNLE9BQU8sUUFBUSxJQUFJO0FBQ3pCLFFBQU0sTUFBTSxRQUFRLE1BQU0sSUFBSTtBQUM5QixRQUFNLFVBQVUsV0FBVyxHQUFHO0FBRTlCLFNBQU87QUFBQSxJQUNMLE1BQU0sUUFBUTtBQUFBLElBQ2Q7QUFBQSxJQUNBLFNBQVM7QUFBQSxNQUNQLE9BQU87QUFBQSxRQUNMLEtBQUtDLFNBQVEsa0NBQVcsT0FBTztBQUFBLFFBQy9CLFlBQVk7QUFBQSxRQUNaLG1CQUFtQkEsU0FBUSxpREFBaUQ7QUFBQSxRQUM1RSxXQUFXO0FBQUEsTUFDYjtBQUFBLElBQ0Y7QUFBQSxJQUNBLFFBQVE7QUFBQSxNQUNOLGNBQWMsS0FBSyxVQUFVLFlBQVk7QUFBQSxJQUMzQztBQUFBLElBQ0EsS0FBSztBQUFBLE1BQ0gscUJBQXFCO0FBQUEsUUFDbkIsTUFBTTtBQUFBLFVBQ0osS0FBSztBQUFBO0FBQUEsUUFDUDtBQUFBLE1BQ0Y7QUFBQSxJQUNGO0FBQUEsSUFDQSxjQUFjO0FBQUEsTUFDWixTQUFTO0FBQUE7QUFBQSxRQUVQO0FBQUEsUUFDQTtBQUFBLFFBQ0E7QUFBQSxRQUNBO0FBQUE7QUFBQSxRQUdBLEdBQUcsdUJBQXVCLEtBQUs7QUFBQTtBQUFBO0FBQUEsUUFHL0I7QUFBQSxRQUNBO0FBQUEsUUFDQTtBQUFBLFFBQ0E7QUFBQSxRQUNBO0FBQUEsUUFDQTtBQUFBLFFBQ0E7QUFBQSxRQUNBO0FBQUEsUUFDQTtBQUFBO0FBQUEsUUFHQTtBQUFBLFFBQ0E7QUFBQTtBQUFBLFFBR0E7QUFBQTtBQUFBLFFBR0E7QUFBQTtBQUFBLFFBR0E7QUFBQTtBQUFBLFFBR0E7QUFBQTtBQUFBLFFBR0E7QUFBQTtBQUFBLFFBR0E7QUFBQTtBQUFBLFFBR0E7QUFBQSxNQUNGO0FBQUE7QUFBQSxNQUdBLFNBQVM7QUFBQTtBQUFBLFFBRVA7QUFBQSxRQUNBO0FBQUE7QUFBQSxRQUdBO0FBQUEsUUFDQTtBQUFBLFFBQ0E7QUFBQSxRQUNBO0FBQUEsUUFDQTtBQUFBLFFBQ0E7QUFBQSxRQUNBO0FBQUEsUUFDQTtBQUFBLFFBQ0E7QUFBQSxRQUNBO0FBQUEsUUFDQTtBQUFBLFFBQ0E7QUFBQTtBQUFBLFFBR0E7QUFBQSxRQUNBO0FBQUE7QUFBQSxRQUdBO0FBQUE7QUFBQSxRQUdBO0FBQUE7QUFBQSxRQUdBO0FBQUE7QUFBQSxRQUdBO0FBQUEsTUFDRjtBQUFBO0FBQUEsTUFHQSxPQUFPLFFBQVEsSUFBSSxhQUFhLGdCQUFnQixRQUFRO0FBQUE7QUFBQSxNQUd4RCxTQUFTLENBQUMsZUFBZSxhQUFhO0FBQUEsSUFDeEM7QUFBQSxJQUNBLFFBQVE7QUFBQSxNQUNOLE1BQU07QUFBQSxNQUNOLE1BQU0sUUFBUTtBQUFBLE1BQ2QsTUFBTSxRQUFRO0FBQUEsTUFDZCxNQUFNO0FBQUE7QUFBQSxNQUdOLElBQUk7QUFBQTtBQUFBLFFBRUYsUUFBUTtBQUFBO0FBQUEsUUFFUixjQUFjO0FBQUEsTUFDaEI7QUFBQTtBQUFBLE1BR0EsUUFBUTtBQUFBLFFBQ04sYUFBYSxDQUFDLGVBQWUsZUFBZSx5QkFBeUIsd0JBQXdCLHFCQUFxQjtBQUFBLE1BQ3BIO0FBQUE7QUFBQSxNQUdBLE9BQU8sWUFBWSxRQUFRLFVBQVU7QUFBQSxJQUN2QztBQUFBLElBQ0EsU0FBUztBQUFBLE1BQ1AsR0FBRyxrQkFBa0IsT0FBTztBQUFBO0FBQUEsTUFFNUJDLFlBQVc7QUFBQSxRQUNULE1BQU07QUFBQSxRQUNOLFVBQVU7QUFBQSxRQUNWLFlBQVk7QUFBQSxRQUNaLFVBQVU7QUFBQSxNQUNaLENBQUM7QUFBQTtBQUFBLE1BRUQ7QUFBQSxRQUNFLE1BQU07QUFBQSxRQUNOLGFBQWE7QUFFWCxnQkFBTSxlQUFlLFFBQVE7QUFDN0Isa0JBQVEsT0FBTyxJQUFJLFNBQWdCO0FBQ2pDLGtCQUFNLFVBQVUsS0FBSyxLQUFLLEdBQUc7QUFFN0IsZ0JBQUksUUFBUSxTQUFTLGVBQWUsS0FBSyxRQUFRLFNBQVMsZ0NBQWdDLEdBQUc7QUFDM0Y7QUFBQSxZQUNGO0FBRUEsZ0JBQUksUUFBUSxTQUFTLGtCQUFrQixLQUFLLFFBQVEsU0FBUyxnQ0FBZ0MsR0FBRztBQUM5RjtBQUFBLFlBQ0Y7QUFDQSx5QkFBYSxNQUFNLFNBQVMsSUFBSTtBQUFBLFVBQ2xDO0FBQUEsUUFDRjtBQUFBLFFBQ0Esa0JBQWtCO0FBRWhCLGdCQUFNLGVBQWUsUUFBUTtBQUM3QixrQkFBUSxPQUFPLElBQUksU0FBZ0I7QUFDakMsa0JBQU0sVUFBVSxLQUFLLEtBQUssR0FBRztBQUM3QixnQkFBSSxRQUFRLFNBQVMsZUFBZSxLQUFLLFFBQVEsU0FBUyxnQ0FBZ0MsR0FBRztBQUMzRjtBQUFBLFlBQ0Y7QUFDQSxnQkFBSSxRQUFRLFNBQVMsa0JBQWtCLEtBQUssUUFBUSxTQUFTLGdDQUFnQyxHQUFHO0FBQzlGO0FBQUEsWUFDRjtBQUNBLHlCQUFhLE1BQU0sU0FBUyxJQUFJO0FBQUEsVUFDbEM7QUFBQSxRQUNGO0FBQUEsTUFDRjtBQUFBLElBQ0Y7QUFBQSxJQUNBLFNBQVM7QUFBQSxNQUNQLE1BQU0sUUFBUSxvQkFBb0IsQ0FBQyxlQUFlLFVBQVUsSUFBSSxDQUFDO0FBQUEsSUFDbkU7QUFBQSxJQUNBLE9BQU87QUFBQSxNQUNMLFFBQVE7QUFBQSxNQUNSLFFBQVE7QUFBQSxNQUNSLFdBQVc7QUFBQTtBQUFBLE1BR1gsc0JBQXNCO0FBQUE7QUFBQSxNQUd0Qix1QkFBdUI7QUFBQTtBQUFBLE1BR3ZCLGFBQWE7QUFBQTtBQUFBLE1BR2IsbUJBQW1CO0FBQUE7QUFBQSxNQUVuQixlQUFlO0FBQUE7QUFBQSxRQUViLE9BQU8sU0FBUyxNQUFNO0FBRXBCLGNBQUksUUFBUSxTQUFTLGtCQUFtQjtBQUV4QyxjQUFJLFFBQVEsU0FBUyxTQUFTLGtCQUFrQixLQUFLLFFBQVEsU0FBUyxTQUFTLGdDQUFnQyxHQUFHO0FBQ2hIO0FBQUEsVUFDRjtBQUNBLGVBQUssT0FBTztBQUFBLFFBQ2Q7QUFBQTtBQUFBLFFBRUEsT0FBTztBQUFBO0FBQUEsUUFHUCxVQUFVLENBQUM7QUFBQSxRQUVYLFFBQVE7QUFBQTtBQUFBLFVBRU4sZ0JBQWdCLGVBQWE7QUFFM0IsZ0JBQUksVUFBVSxNQUFNLFNBQVMsUUFBUSxHQUFHO0FBQ3RDLHFCQUFPO0FBQUEsWUFDVDtBQUNBLGdCQUFJLFVBQVUsTUFBTSxTQUFTLE9BQU8sR0FBRztBQUNyQyxxQkFBTztBQUFBLFlBQ1Q7QUFDQSxtQkFBTztBQUFBLFVBQ1Q7QUFBQSxVQUNBLGdCQUFnQjtBQUFBLFVBQ2hCLGdCQUFnQixlQUFhO0FBRTNCLGtCQUFNLFVBQVUsVUFBVSxNQUFNLE1BQU0sR0FBRyxFQUFFLElBQUksS0FBSztBQUNwRCxnQkFBSSxDQUFDLE9BQU8sT0FBTyxRQUFRLE9BQU8sT0FBTyxNQUFNLEVBQUUsU0FBUyxPQUFPLEdBQUc7QUFDbEUscUJBQU87QUFBQSxZQUNUO0FBQ0EsZ0JBQUksQ0FBQyxRQUFRLFNBQVMsT0FBTyxLQUFLLEVBQUUsU0FBUyxPQUFPLEdBQUc7QUFDckQscUJBQU87QUFBQSxZQUNUO0FBQ0EsZ0JBQUksQ0FBQyxLQUFLLEVBQUUsU0FBUyxPQUFPLEdBQUc7QUFDN0IscUJBQU87QUFBQSxZQUNUO0FBQ0EsbUJBQU87QUFBQSxVQUNUO0FBQUE7QUFBQSxVQUdBLGNBQWMsUUFBTTtBQUVsQixnQkFBSSxHQUFHLFNBQVMsS0FBSyxLQUFLLENBQUMsR0FBRyxTQUFTLGNBQWMsR0FBRztBQUN0RCxxQkFBTztBQUFBLFlBQ1Q7QUFDQSxnQkFBSSxHQUFHLFNBQVMsWUFBWSxHQUFHO0FBQzdCLHFCQUFPO0FBQUEsWUFDVDtBQUNBLGdCQUFJLEdBQUcsU0FBUyxPQUFPLEdBQUc7QUFDeEIscUJBQU87QUFBQSxZQUNUO0FBR0EsZ0JBQUksb0JBQW9CLEVBQUUsR0FBRztBQUMzQixxQkFBTyx3QkFBd0IsRUFBRTtBQUFBLFlBQ25DO0FBR0EsZ0JBQUksR0FBRyxTQUFTLFVBQVUsR0FBRztBQUMzQixxQkFBTztBQUFBLFlBQ1Q7QUFHQSxnQkFBSSxHQUFHLFNBQVMsT0FBTyxLQUFLLEdBQUcsU0FBUyxPQUFPLEtBQUssR0FBRyxTQUFTLFFBQVEsR0FBRztBQUN6RSxxQkFBTztBQUFBLFlBQ1Q7QUFDQSxnQkFBSSxHQUFHLFNBQVMsV0FBVyxLQUFLLEdBQUcsU0FBUyxLQUFLLEtBQUssR0FBRyxTQUFTLElBQUksR0FBRztBQUN2RSxxQkFBTztBQUFBLFlBQ1Q7QUFHQSxnQkFBSSxHQUFHLFNBQVMsU0FBUyxLQUFLLEdBQUcsU0FBUyxPQUFPLEdBQUc7QUFDbEQscUJBQU87QUFBQSxZQUNUO0FBR0EsZ0JBQUksR0FBRyxTQUFTLGNBQWMsS0FBSyxHQUFHLFNBQVMsY0FBYyxHQUFHO0FBQzlELHFCQUFPO0FBQUEsWUFDVDtBQUdBLGdCQUFJLEdBQUcsU0FBUyxVQUFVLEdBQUc7QUFDM0IscUJBQU87QUFBQSxZQUNUO0FBR0EsZ0JBQUksR0FBRyxTQUFTLGNBQWMsR0FBRztBQUMvQixxQkFBTztBQUFBLFlBQ1Q7QUFHQSxnQkFBSSxHQUFHLFNBQVMsWUFBWSxHQUFHO0FBQzdCLG9CQUFNLFFBQVEsR0FBRyxNQUFNLHNCQUFzQjtBQUM3QyxrQkFBSSxPQUFPO0FBQ1QsdUJBQU8sU0FBUyxNQUFNLENBQUMsQ0FBQztBQUFBLGNBQzFCO0FBQUEsWUFDRjtBQUdBLGdCQUFJLEdBQUcsU0FBUyxpQkFBaUIsR0FBRztBQUNsQyxxQkFBTztBQUFBLFlBQ1Q7QUFHQSxnQkFBSSxHQUFHLFNBQVMsWUFBWSxLQUFLLEdBQUcsU0FBUyxZQUFZLEdBQUc7QUFDMUQscUJBQU87QUFBQSxZQUNUO0FBQUEsVUFDRjtBQUFBLFFBQ0Y7QUFBQSxNQUNGO0FBQUEsSUFDRjtBQUFBLEVBQ0Y7QUFDRixDQUFDOyIsCiAgIm5hbWVzIjogWyJyZXNvbHZlIiwgInZpc3VhbGl6ZXIiLCAicmVzb2x2ZSIsICJ2aXN1YWxpemVyIl0KfQo=
