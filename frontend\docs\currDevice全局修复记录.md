# currDevice 全局修复记录

## 修复概述

系统性地排查并修复了项目中不正确使用 `currDevice` 的问题。在设备相关组件中，应该使用传入的 `props.deviceId` 参数而不是全局状态中的 `currDevice`。

## 修复原理

**问题根源**：
- `currDevice` 是全局状态中当前选中的设备
- 在具体的设备组件中，应该使用传入的 `props.deviceId` 参数
- 这样确保每个设备组件操作的是正确的设备数据，而不是全局当前选中的设备

**修复策略**：
1. 移除不必要的 `currDevice` 解构导入
2. 将 `currDevice.id` 替换为 `props.deviceId` 或 `props.deviceModel.id`
3. 为对话框组件添加 `deviceId` 参数传递

## 修复的文件列表

### 1. AddCustomPointGroupDialog.vue
**修复内容**：
- 添加 `deviceId` props 参数
- 移除 `currDevice` 解构导入
- 将所有 `currDevice.id` 替换为 `props.deviceId`
- 在 Device.vue 中传入 `device-id` 参数

**修复位置**：
- 第96行：添加 props 定义
- 第123行：API 调用中的设备ID
- 第168行：设备切换检查
- 第202行：数据加载
- 第301行：单个组类型数据加载
- 第369行、373行：测试API调用

### 2. FileDownload.vue
**修复内容**：
- 移除 `currDevice` 解构导入
- 修复 API 调用错误（多余的括号）
- 将本地存储中的设备ID使用替换为 `props.deviceId`

**修复位置**：
- 第141行：移除 currDevice 解构
- 第280行：修复 API 调用
- 第889-891行：组件卸载时的本地存储
- 第914-915行：保存数据函数
- 第920行、924行：加载数据函数

### 3. FileUpload.vue
**修复内容**：
- 移除 `currDevice` 解构导入
- 将本地存储操作中的设备ID替换为 `props.deviceId`

**修复位置**：
- 第91行：移除 currDevice 解构
- 第508行、512行、519行：本地存储操作

### 4. ParamSetting.vue
**修复内容**：
- 移除 `currDevice` 解构导入
- 将所有设备相关操作中的 `currDevice.id` 替换为 `props.deviceId`

**修复位置**：
- 第83行：移除 currDevice 解构
- 第31行：模板中的设备功能码检查
- 第255行：参数组名获取
- 第454行：差异参数查询
- 第476行：导出文件名生成
- 第501行：参数导出
- 第698行：编辑区域确认

### 5. RemoteDrive.vue
**修复内容**：
- 移除 `currDevice` 解构导入
- 设备参数获取已在之前修复

**修复位置**：
- 第44行：移除 currDevice 解构

### 6. RemoteYt.vue
**修复内容**：
- 移除 `currDevice` 解构导入
- 设备参数获取已在之前修复

**修复位置**：
- 第46行：移除 currDevice 解构

### 7. Device.vue
**修复内容**：
- 移除 `currDevice` 解构导入
- 将所有自定义菜单和报告操作中的 `currDevice.id` 替换为 `props.deviceModel.id`
- 为 AddCustomPointGroupDialog 添加设备ID传递

**修复位置**：
- 第150行：移除 currDevice 解构
- 第62-68行：为对话框添加 device-id 参数
- 第445行、454行：菜单编辑和添加
- 第487行：菜单删除
- 第659行：报告删除
- 第687行：点组删除
- 第712行：获取所有组数据
- 第733行：点组确认日志
- 第780行：点组添加
- 第842行：点组编辑
- 第875行：刷新自定义组

### 8. ReportOperate.vue 和 ReportAuditLog.vue
**修复内容**：
- 移除 `currDevice` 解构导入
- 将全局报告获取中的设备ID替换为 `props.deviceId`

### 9. RemoteYm.vue
**修复内容**：
- 添加 `deviceId` props 参数
- 移除 `currDevice` 解构导入
- 将设备参数获取中的 `currDevice.id` 替换为 `props.deviceId`
- 修复 API 调用使用正确的设备ID参数

### 10. RemoteControl.vue
**修复内容**：
- 移除 `currDevice` 解构导入
- 将设备参数获取中的 `currDevice.id` 替换为 `props.deviceId`

### 11. GroupInfo.vue
**修复内容**：
- 添加 `deviceId` props 参数
- 移除 `currDevice` 解构导入
- 将组信息查询中的 `currDevice.id` 替换为 `props.deviceId`

### 12. RemoteSignal.vue
**修复内容**：
- 将导出功能中的设备参数获取替换为 `props.deviceId`

### 13. CustomGroup.vue
**修复内容**：
- 添加 `deviceId` props 参数
- 移除 `currDevice` 解构导入
- 将自定义组查询中的 `currDevice.id` 替换为 `props.deviceId`

## 验证方法

1. **多设备测试**：
   - 连接多个设备
   - 切换不同设备标签页
   - 确认每个设备的操作都作用于正确的设备

2. **功能测试**：
   - 参数设置功能
   - 文件上传/下载功能
   - 遥控/遥信操作
   - 自定义菜单和报告操作
   - 本地存储功能

3. **错误检查**：
   - 检查控制台无相关错误
   - 确认API调用使用正确的设备ID
   - 验证本地存储使用正确的设备标识

## 修复效果

✅ **解决了设备ID混乱问题**：每个设备组件现在都使用正确的设备ID进行操作

✅ **提高了代码可靠性**：移除了对全局状态的不必要依赖

✅ **保持了功能完整性**：所有设备相关功能继续正常工作

✅ **改善了多设备支持**：在多设备环境下操作更加准确

修复完成后，所有设备相关组件都将使用传入的设备ID参数，确保操作的准确性和可靠性。
