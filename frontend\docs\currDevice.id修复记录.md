# currDevice.id 修复记录

## 修复概述

全局检查并修复了项目中不正确使用 `currDevice.id` 的地方，将其替换为正确的 `props.deviceId` 参数。

## 修复的文件列表

### 1. RemoteTelemetry.vue
**位置**: `frontend/src/views/biz/debug/device/views/RemoteTelemetry.vue:132`
**修复前**:
```typescript
const names = [debugIndex.compData.get(currDevice.id).pname, debugIndex.compData.get(currDevice.id).name];
```
**修复后**:
```typescript
const names = [debugIndex.compData.get(props.deviceId).pname, debugIndex.compData.get(props.deviceId).name];
```

### 2. RemoteDrive.vue
**位置**: `frontend/src/views/biz/debug/device/views/RemoteDrive.vue:117-121`
**修复前**:
```typescript
const param: MenuIdName = {
  id: currDevice.id,
  type: "submenu",
  names: [debugIndex.compData.get(currDevice.id).pname, debugIndex.compData.get(currDevice.id).name]
};
```
**修复后**:
```typescript
const param: MenuIdName = {
  id: props.deviceId,
  type: "submenu", 
  names: [debugIndex.compData.get(props.deviceId).pname, debugIndex.compData.get(props.deviceId).name]
};
```

### 3. RemoteYt.vue
**位置**: `frontend/src/views/biz/debug/device/views/RemoteYt.vue:204-208`
**修复前**:
```typescript
const param: MenuIdName = {
  id: currDevice.id,
  type: "submenu",
  names: [debugIndex.compData.get(currDevice.id).pname, debugIndex.compData.get(currDevice.id).name]
};
```
**修复后**:
```typescript
const param: MenuIdName = {
  id: props.deviceId,
  type: "submenu",
  names: [debugIndex.compData.get(props.deviceId).pname, debugIndex.compData.get(props.deviceId).name]
};
```

### 4. Device.vue
**位置**: `frontend/src/views/biz/debug/device/components/Device.vue`

**修复1 - 第538行**:
```typescript
// 修复前
const res = await customInfoApi.getLGReportsByDevice(currDevice.id);
// 修复后
const res = await customInfoApi.getLGReportsByDevice(props.deviceModel.id);
```

**修复2 - 第613行和第622行**:
```typescript
// 修复前
const result = await customInfoApi.editReportByDevice(currDevice.id, ...);
const result = await customInfoApi.addReportByDevice(currDevice.id, ...);
// 修复后
const result = await customInfoApi.editReportByDevice(props.deviceModel.id, ...);
const result = await customInfoApi.addReportByDevice(props.deviceModel.id, ...);
```

### 5. ReportOperate.vue
**位置**: `frontend/src/views/biz/debug/device/views/ReportOperate.vue:78-79`
**修复前**:
```typescript
const { report, currDevice, addConsole } = useDebugStore();
const globalReport = report.get(currDevice.id);
```
**修复后**:
```typescript
const { report, addConsole } = useDebugStore();
const globalReport = report.get(props.deviceId);
```

### 6. ReportAuditLog.vue
**位置**: `frontend/src/views/biz/debug/device/views/ReportAuditLog.vue:80-81`
**修复前**:
```typescript
const { report, currDevice, addConsole } = useDebugStore();
const globalReport = report.get(currDevice.id);
```
**修复后**:
```typescript
const { report, addConsole } = useDebugStore();
const globalReport = report.get(props.deviceId);
```

### 7. ParamSetting.vue
**位置**: `frontend/src/views/biz/debug/device/views/ParamSetting.vue:166`
**修复前**:
```typescript
if (debugIndex.compData.get(currDevice.id).fc === "SG") {
```
**修复后**:
```typescript
if (debugIndex.compData.get(props.deviceId).fc === "SG") {
```

### 8. FileUpload.vue
**位置**: `frontend/src/views/biz/debug/device/views/FileUpload.vue:507-520`
**修复前**:
```typescript
function saveData() {
  console.log(currDevice.id);
  localStorage.setItem(currDevice.id + ".uploadPath", JSON.stringify(filePath.value));
}

const loadData = async () => {
  console.log(currDevice.id);
  setTimeout(() => {
    const filePathValue = localStorage.getItem(currDevice.id + ".uploadPath");
    // ...
  }, 100);
};
```
**修复后**:
```typescript
function saveData() {
  console.log(props.deviceId);
  localStorage.setItem(props.deviceId + ".uploadPath", JSON.stringify(filePath.value));
}

const loadData = async () => {
  console.log(props.deviceId);
  setTimeout(() => {
    const filePathValue = localStorage.getItem(props.deviceId + ".uploadPath");
    // ...
  }, 100);
};
```

## 保留的正确使用

以下文件中的 `currDevice.id` 使用是正确的，因为它们用于比较当前设备状态：

1. `DeviceList.vue` (debug模块) - 用于比较断开连接的设备是否为当前设备
2. `DeviceList.vue` (hmi模块) - 同上
3. `Devices.vue` - 用于设备标签页切换逻辑

## 修复原理

**问题原因**: 
- `currDevice.id` 引用的是全局状态中当前选中的设备ID
- 但在具体的设备组件中，应该使用传入的 `props.deviceId` 参数
- 这样可以确保每个设备组件操作的是正确的设备数据

**修复策略**:
- 将 `currDevice.id` 替换为 `props.deviceId` 或 `props.deviceModel.id`
- 移除不必要的 `currDevice` 解构导入
- 保持设备状态比较等正确使用场景不变

## 验证方法

1. 启动应用并连接多个设备
2. 切换不同设备标签页
3. 确认每个设备的操作（参数设置、报告操作等）都作用于正确的设备
4. 检查控制台无相关错误信息

修复完成后，所有设备相关操作都将正确地作用于当前选中的设备。
