<template>
  <el-dialog v-model="visible" width="1200px">
    <template #title>
      <svg-icon icon="ep:collection" style="margin-right: 6px; color: var(--el-color-primary); vertical-align: middle" />
      {{ isEdit ? t("device.customMenu.editPointGroup") : t("device.customMenu.addPointGroup") }}
    </template>
    <div class="dialog-body">
      <!-- 顶部表单 -->
      <div class="top-form">
        <el-form :model="form" :rules="rules" ref="formRef" :inline="true" class="form-inline">
          <el-form-item :label="t('device.customMenu.menuName')" prop="name">
            <el-input v-model="form.name" :placeholder="t('device.customMenu.inputMenuName')" style="width: 200px" />
          </el-form-item>
          <el-form-item :label="t('device.customMenu.menuDesc')" prop="desc">
            <el-input v-model="form.desc" :placeholder="t('device.customMenu.inputMenuDesc')" style="width: 300px" />
          </el-form-item>
          <el-form-item :label="t('device.customMenu.selectGroupType')" prop="groupType">
            <el-select v-model="form.groupType" clearable style="width: 200px" @change="onGroupTypeChange" :loading="isLoadingData">
              <el-option value="ST" :label="t('device.customMenu.groupTypes.ST')" />
              <el-option value="MX" :label="t('device.customMenu.groupTypes.MX')" />
              <el-option value="SP" :label="t('device.customMenu.groupTypes.SP')" />
              <el-option value="SG" :label="t('device.customMenu.groupTypes.SG')" />
            </el-select>
          </el-form-item>
        </el-form>
      </div>
      <!-- 选择器区域 -->
      <div class="selector-area">
        <div class="selector-header">
          <div style="display: flex; gap: 12px; align-items: center">
            <el-input v-model="filterText" :placeholder="t('device.customMenu.filterPlaceholder')" clearable style="width: 300px" />
            <el-button type="primary" plain :icon="Refresh" @click="forceReloadData" :loading="isLoadingData" size="small">
              {{ t("device.customMenu.refreshData") }}
            </el-button>
          </div>
        </div>
        <div class="selector-content" v-loading="isLoadingData" :element-loading-text="loadingProgress || t('device.customMenu.loadingData')">
          <div class="left">
            <div class="title">{{ t("device.customMenu.selectGroupType") }}</div>
            <el-tabs v-model="activeTab" type="card" class="fc-tabs" v-if="!isLoadingData && fcTabs.length > 0">
              <el-tab-pane v-for="tab in fcTabs" :key="tab.name" :label="tab.desc || tab.name" :name="tab.name">
                <el-table :data="filteredItems(tab.items)" height="320" @row-dblclick="onAddItem" @row-click="onRowSelect">
                  <el-table-column type="index" width="50" />
                  <el-table-column prop="name" :label="t('device.groupInfo.table.name')" width="200" show-overflow-tooltip />
                  <el-table-column prop="desc" :label="t('device.groupInfo.table.desc')" min-width="200" show-overflow-tooltip />
                  <el-table-column width="80" align="center">
                    <template #default="{ row }">
                      <el-button link type="primary" @click="onAddItem(row)">{{ t("common.add") }}</el-button>
                    </template>
                  </el-table-column>
                </el-table>
              </el-tab-pane>
            </el-tabs>
            <div v-else-if="!isLoadingData && form.groupType && fcTabs.length === 0" class="empty-data">
              <el-empty :description="t('device.customMenu.noDataForGroupType')" />
            </div>
            <div v-else-if="!isLoadingData && !form.groupType" class="empty-data">
              <el-empty :description="t('device.customMenu.pleaseSelectGroupType')" />
            </div>
          </div>
          <div class="mid">
            <el-button type="primary" plain :disabled="!candidateToAdd" @click="candidateToAdd && onAddItem(candidateToAdd)">→</el-button>
            <el-button type="danger" plain :disabled="!candidateToRemove" @click="candidateToRemove && onRemoveItem(candidateToRemove)">←</el-button>
          </div>
          <div class="right">
            <div class="title">{{ t("device.customMenu.selectedPoints") }} ({{ selectedItems.length }})</div>
            <el-table :data="selectedItems" height="360" @row-dblclick="onRemoveItem" @row-click="onSelectedRowSelect">
              <el-table-column type="index" width="50" />
              <el-table-column prop="name" :label="t('device.groupInfo.table.name')" width="200" show-overflow-tooltip />
              <el-table-column prop="desc" :label="t('device.groupInfo.table.desc')" min-width="200" show-overflow-tooltip />
              <el-table-column width="80" align="center">
                <template #default="{ row }">
                  <el-button link type="danger" @click="onRemoveItem(row)">{{ t("common.remove") }}</el-button>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </div>
      </div>
    </div>
    <template #footer>
      <el-button @click="onClose">{{ t("device.customMenu.cancel") }}</el-button>
      <el-button type="primary" @click="onConfirm">{{ t("device.customMenu.confirm") }}</el-button>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, watch } from "vue";
import { useI18n } from "vue-i18n";
import { ElMessage } from "element-plus";
import { Refresh } from "@element-plus/icons-vue";
import { customInfoApi, type SelectedItem, type CustomGroup } from "@/api/modules/biz/debug/custominfo";
import { getUUID } from "@/utils";
const { t } = useI18n();

const props = defineProps<{ deviceId: string }>();
const emit = defineEmits<{ (e: "confirm", group: CustomGroup, isEdit: boolean): void }>();
const visible = defineModel<boolean>("visible", { default: false });
const isEdit = defineModel<boolean>("isEdit", { default: false });

const modelValue = defineModel<CustomGroup | null>("model", { default: null });

const formRef = ref();
const form = reactive<{ uuid?: string; name: string; desc: string; groupType?: string }>({ name: "", desc: "", groupType: undefined });

// 异步验证菜单名称唯一性
const validateMenuName = async (rule: any, value: string, callback: any) => {
  if (!value || value.trim() === "") {
    callback(new Error(t("device.customMenu.inputMenuName")));
    return;
  }

  // 编辑模式下，如果名称没有改变，跳过验证
  if (isEdit.value && modelValue.value && modelValue.value.name === value) {
    callback();
    return;
  }

  try {
    // 验证用户输入的名称（将作为 newname 保存）
    const result = await customInfoApi.validateMenuNameByDevice(props.deviceId, value.trim());
    if (result.code === 0) {
      if (result.data.isValid) {
        callback();
      } else {
        callback(new Error(result.data.message || "名称验证失败"));
      }
    } else {
      callback(new Error("名称验证失败，请重试"));
    }
  } catch (error) {
    console.error("名称验证出错:", error);
    callback(new Error("名称验证失败，请重试"));
  }
};

const rules = {
  name: [
    { required: true, message: t("device.customMenu.inputMenuName"), trigger: "blur" },
    { validator: validateMenuName, trigger: "blur" }
  ],
  desc: [{ required: true, message: t("device.customMenu.inputMenuDesc"), trigger: ["blur", "change"] }],
  groupType: [{ required: true, message: t("device.customMenu.selectGroupType"), trigger: ["blur", "change"] }]
};

const groupTypes = computed(() => [
  { value: "ST", label: t("device.customMenu.groupTypes.ST") },
  { value: "MX", label: t("device.customMenu.groupTypes.MX") },
  { value: "SP", label: t("device.customMenu.groupTypes.SP") },
  { value: "SG", label: t("device.customMenu.groupTypes.SG") }
]);
const allMenusData = ref<{ [groupType: string]: { name: string; desc: string; items: SelectedItem[] }[] }>({});
const fcTabs = ref<{ name: string; desc: string; items: SelectedItem[] }[]>([]);
const activeTab = ref<string>("");
const filterText = ref("");
const selectedItems = ref<SelectedItem[]>([]);
const candidateToAdd = ref<SelectedItem | null>(null);
const candidateToRemove = ref<SelectedItem | null>(null);
const isLoadingData = ref<boolean>(false);
const loadingProgress = ref<string>("");
const lastDeviceId = ref<string>("");

watch(visible, async v => {
  if (v) {
    // 检查是否需要重新加载数据（设备切换或首次加载）
    const currentDeviceId = props.deviceId;
    if (lastDeviceId.value !== currentDeviceId || Object.keys(allMenusData.value).length === 0) {
      console.log("设备切换或首次加载，重新加载所有数据");
      lastDeviceId.value = currentDeviceId;
      await loadAllData();
    } else {
      console.log("使用已缓存的数据，无需重新加载");
    }

    if (isEdit.value && modelValue.value) {
      form.uuid = modelValue.value.uuid;
      form.name = modelValue.value.name || "";
      form.desc = modelValue.value.desc || "";
      form.groupType = modelValue.value.keyword || undefined;
      selectedItems.value = Array.isArray(modelValue.value.items) ? [...modelValue.value.items] : [];
      if (form.groupType) onGroupTypeChange(form.groupType);
    } else {
      form.uuid = getUUID();
      form.name = "";
      form.desc = "";
      form.groupType = undefined;
      selectedItems.value = [];
      activeTab.value = "";
      fcTabs.value = [];
    }
  }
});

async function loadAllData() {
  if (isLoadingData.value) return;
  isLoadingData.value = true;
  loadingProgress.value = t("device.customMenu.loadingGroupTypeData");

  try {
    const deviceId = props.deviceId;
    console.log("loadAllData - 开始加载，设备ID:", deviceId);

    // 清空之前的缓存
    allMenusData.value = {};

    // 加载所有组类型的数据
    loadingProgress.value = `${t("device.customMenu.loadingGroupTypes")} ${groupTypes.value.length}...`;
    console.log(`开始并行加载 ${groupTypes.value.length} 个组类型的数据`);

    const loadPromises = groupTypes.value.map(async (groupType, index) => {
      try {
        console.log(`开始加载组类型: ${groupType.value}`);
        const res = await customInfoApi.getMenusByFcByDevice(deviceId, groupType.value);
        console.log(`组类型 ${groupType.value} 的API响应:`, res);

        if (res.code === 0) {
          const menus = Array.isArray(res.data) ? res.data : [];
          console.log(`组类型 ${groupType.value} 的原始菜单数据:`, menus);

          const processedData = menus.map((m: any) => ({
            name: m.name,
            desc: m.desc,
            items: (m.items || []).map((it: any) => ({
              name: it.name,
              desc: it.desc,
              grp: it.grp,
              inf: it.inf,
              fc: m.fc || groupType.value,
              unit: it.unit,
              type: it.type
            }))
          }));

          allMenusData.value[groupType.value] = processedData;
          console.log(`组类型 ${groupType.value} 处理后的数据已缓存:`, processedData);
          loadingProgress.value = `${t("device.customMenu.loadedGroupTypes")} ${index + 1}/${groupTypes.value.length}`;
        } else {
          console.error(`组类型 ${groupType.value} API返回错误:`, res.msg);
        }
      } catch (e) {
        console.error(`加载组类型 ${groupType.value} 数据失败:`, e);
      }
    });

    await Promise.all(loadPromises);
    loadingProgress.value = t("device.customMenu.dataLoadComplete");
    console.log("所有组类型数据加载完成:", allMenusData.value);
  } catch (e) {
    console.error("loadAllData failed", e);
    loadingProgress.value = t("device.customMenu.dataLoadFailed");
  } finally {
    setTimeout(() => {
      isLoadingData.value = false;
      loadingProgress.value = "";
    }, 300); // 短暂延迟让用户看到完成状态
  }
}

function onGroupTypeChange(value?: string) {
  console.log(`onGroupTypeChange 被调用, value: ${value}`);
  console.log(`当前缓存的组类型列表:`, Object.keys(allMenusData.value));

  if (!value) {
    fcTabs.value = [];
    activeTab.value = "";
    return;
  }

  // 显示加载状态（即使从缓存加载也显示，提供更好的用户反馈）
  isLoadingData.value = true;
  loadingProgress.value = `${t("device.customMenu.switchingToGroupType")} ${value}...`;
  fcTabs.value = [];
  activeTab.value = "";

  // 从缓存数据中获取对应组类型的数据
  const groupTypeData = allMenusData.value[value];
  console.log(`查找组类型 ${value} 的缓存数据:`, groupTypeData);

  if (groupTypeData && groupTypeData.length > 0) {
    // 从缓存加载，显示短暂的加载状态
    setTimeout(() => {
      fcTabs.value = groupTypeData;
      activeTab.value = fcTabs.value[0]?.name || "";
      isLoadingData.value = false;
      loadingProgress.value = "";
      console.log(`切换到组类型 ${value}, 数据已从缓存加载:`, fcTabs.value);
    }, 300); // 显示300ms的加载状态
  } else {
    // 如果缓存中没有数据，重新加载这个组类型
    console.warn(`组类型 ${value} 的数据不在缓存中或为空，尝试重新加载`);
    loadingProgress.value = `${t("device.customMenu.loadingGroupTypeDataSingle")} ${value}...`;
    loadSingleGroupTypeData(value);
  }
}

async function loadSingleGroupTypeData(groupType: string) {
  try {
    console.log(`loadSingleGroupTypeData - 开始加载组类型: ${groupType}`);
    const deviceId = props.deviceId;
    const res = await customInfoApi.getMenusByFcByDevice(deviceId, groupType);
    console.log(`loadSingleGroupTypeData - 组类型 ${groupType} API响应:`, res);

    if (res.code === 0) {
      const menus = Array.isArray(res.data) ? res.data : [];
      console.log(`loadSingleGroupTypeData - 组类型 ${groupType} 原始数据:`, menus);

      const groupTypeData = menus.map((m: any) => ({
        name: m.name,
        desc: m.desc,
        items: (m.items || []).map((it: any) => ({
          name: it.name,
          desc: it.desc,
          grp: it.grp,
          inf: it.inf,
          fc: m.fc || groupType,
          unit: it.unit,
          type: it.type
        }))
      }));

      // 更新缓存
      allMenusData.value[groupType] = groupTypeData;
      console.log(`loadSingleGroupTypeData - 组类型 ${groupType} 数据已缓存:`, groupTypeData);

      // 如果当前选中的组类型就是这个，更新显示
      if (form.groupType === groupType) {
        fcTabs.value = groupTypeData;
        activeTab.value = fcTabs.value[0]?.name || "";
        console.log(`loadSingleGroupTypeData - 组类型 ${groupType} 数据已显示`);
      }
    } else {
      console.error(`loadSingleGroupTypeData - 组类型 ${groupType} API返回错误:`, res.msg);
      ElMessage.warning(`${t("device.customMenu.loadGroupTypeFailed")} ${groupType}: ${res.msg}`);
    }
  } catch (e) {
    console.error(`loadSingleGroupTypeData for ${groupType} failed:`, e);
    ElMessage.error(`${t("device.customMenu.loadGroupTypeError")} ${groupType}`);
  } finally {
    // 关闭加载状态
    isLoadingData.value = false;
    loadingProgress.value = "";
    console.log(`loadSingleGroupTypeData - 组类型 ${groupType} 加载完成`);
  }
}

// 强制重新加载数据
async function forceReloadData() {
  console.log("forceReloadData - 强制刷新数据");
  // 清空缓存
  allMenusData.value = {};
  fcTabs.value = [];
  activeTab.value = "";

  // 重新加载所有数据
  await loadAllData();

  // 如果当前选择了组类型，重新应用选择
  if (form.groupType) {
    onGroupTypeChange(form.groupType);
  }
}

// 测试API调用
async function testApiCall() {
  try {
    console.log("testApiCall - 测试获取所有自定义组");
    const result = await customInfoApi.getAllGroupsByDevice(props.deviceId);
    console.log("testApiCall - getAllGroupsByDevice 结果:", result);

    console.log("testApiCall - 测试获取组类型列表");
    const groupTypeResult = await customInfoApi.getFcListByDevice(props.deviceId);
    console.log("testApiCall - getFcListByDevice 结果:", groupTypeResult);
  } catch (e) {
    console.error("testApiCall - API调用失败:", e);
  }
}

// 在开发环境下暴露测试函数
if (process.env.NODE_ENV === "development") {
  (window as any).testCustomGroupApi = testApiCall;
}

function filteredItems(items: SelectedItem[]) {
  if (!filterText.value) return items.filter(i => !selectedItems.value.some(s => s.name === i.name));
  const text = filterText.value.toLowerCase();
  return items.filter(
    i => (i.name?.toLowerCase()?.includes(text) || i.desc?.toLowerCase()?.includes(text)) && !selectedItems.value.some(s => s.name === i.name)
  );
}

function onRowSelect(row: SelectedItem) {
  candidateToAdd.value = row;
}

function onSelectedRowSelect(row: SelectedItem) {
  candidateToRemove.value = row;
}

function onAddItem(row: SelectedItem | null) {
  if (!row) return;
  if (!selectedItems.value.some(i => i.name === row.name)) {
    selectedItems.value.push({ ...row });
  }
  candidateToAdd.value = null;
}

function onRemoveItem(row: SelectedItem) {
  if (!row) return;
  selectedItems.value = selectedItems.value.filter(i => i.name !== row.name);
  candidateToRemove.value = null;
}

function onClose() {
  visible.value = false;
}

function onConfirm() {
  console.log("onConfirm - 开始提交自定义组");
  console.log("onConfirm - 当前表单数据:", form);

  // 确保选择了至少一个点
  if (selectedItems.value.length === 0) {
    console.error("onConfirm - 未选择任何点");
    ElMessage.error("请至少选择一个点");
    return;
  }

  // 使用表单验证（包括异步名称验证）
  formRef.value?.validate((valid: boolean, fields: any) => {
    console.log("onConfirm - Element Plus 表单校验结果:", { valid, fields });

    if (!valid) {
      console.error("自定义组表单校验失败:", fields);
      ElMessage.error("请填写完整的表单信息");
      return; // 校验失败，不关闭对话框
    }

    console.log("onConfirm - 所有校验通过，继续处理");

    try {
      const payload: CustomGroup = {
        uuid: form.uuid || getUUID(),
        name: form.name, // 保留原始名称
        newname: form.name, // 用户输入的名称作为显示名称
        desc: form.desc,
        fc: form.groupType, // 使用用户选择的组类型
        keyword: form.groupType || "", // 将选择的组类型作为keyword保存
        inherit: undefined as any,
        items: selectedItems.value.map(item => ({
          name: item.name,
          desc: item.desc,
          grp: item.grp,
          inf: item.inf,
          fc: item.fc,
          unit: item.unit,
          type: item.type
        }))
      } as any;

      console.log("AddCustomPointGroupDialog - 提交数据:", payload);
      console.log("AddCustomPointGroupDialog - 选中的点数量:", selectedItems.value.length);
      console.log("AddCustomPointGroupDialog - 选中的组类型:", form.groupType);
      console.log("AddCustomPointGroupDialog - 是否编辑模式:", isEdit.value);

      // 通过 emit 通知父组件提交（父组件调用 addMenu/editMenu）
      emit("confirm", payload, isEdit.value);

      // 不在这里关闭对话框，由父组件处理成功后关闭
    } catch (e) {
      // 数据处理失败时，不关闭对话框
      console.error("AddCustomPointGroupDialog - 数据处理失败:", e);
      ElMessage.error(t("device.customMenu.errorAction"));
    }
  });
}

// moved defineEmits to top to avoid redeclaration
</script>

<style scoped>
.dialog-body {
  display: flex;
  flex-direction: column;
  gap: 16px;
}
.top-form {
  border-bottom: 1px solid var(--el-border-color-light);
  padding-bottom: 16px;
}
.form-inline {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
  align-items: flex-start;
}
.form-inline .el-form-item {
  margin-bottom: 0;
}
.selector-area {
  display: flex;
  flex: 1;
  flex-direction: column;
  min-width: 0;
}
.selector-header {
  margin-bottom: 12px;
}
.selector-content {
  display: flex;
  gap: 12px;
  height: 400px;
}
.selector-content .left {
  flex: 1.2;
  min-width: 0;
  display: flex;
  flex-direction: column;
}
.selector-content .mid {
  display: flex;
  flex-direction: column;
  gap: 10px;
  align-items: center;
  justify-content: center;
  width: 60px;
  flex-shrink: 0;
}
.selector-content .right {
  flex: 1.2;
  min-width: 0;
  display: flex;
  flex-direction: column;
}
.title {
  margin: 6px 0;
  font-weight: 600;
  font-size: 14px;
}
.fc-tabs {
  flex: 1;
  display: flex;
  flex-direction: column;
}
.fc-tabs :deep(.el-tabs__content) {
  flex: 1;
  overflow: hidden;
}
.fc-tabs :deep(.el-tab-pane) {
  height: 100%;
}
.empty-data {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 320px;
  background: var(--el-bg-color-page);
  border-radius: 4px;
}
</style>
