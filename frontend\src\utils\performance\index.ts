/**
 * 性能监控工具统一导出
 */

// 授权性能监控
export {
  authPerformanceMonitor,
  markCheckAuthStart,
  markCheckAuthEnd,
  markLanguageSyncStart,
  markLanguageSyncEnd,
  markRouteLoadStart,
  markRouteLoadEnd,
  markDeviceControllerInitStart,
  markDeviceControllerInitEnd,
  generateAuthPerformanceReport
} from './authPerformance';

// 启动性能监控
export {
  performanceMonitor,
  markAppMounted,
  markRouteReady,
  markResourcesLoaded
} from './performance';

// 组件切换性能监控
export {
  componentSwitchMonitor,
  type SwitchMetrics
} from './componentSwitchMonitor';
