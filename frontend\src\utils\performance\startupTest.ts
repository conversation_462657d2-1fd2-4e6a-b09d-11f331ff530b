/**
 * 启动性能测试工具
 * 用于测试应用启动过程中各个环节的性能
 */

interface TestResult {
  name: string;
  duration: number;
  success: boolean;
  data?: any;
  error?: string;
}

interface TestSuite {
  name: string;
  tests: TestResult[];
  totalDuration: number;
  successCount: number;
  failureCount: number;
}

class StartupPerformanceTester {
  private testResults: TestSuite[] = [];
  private currentSuite: TestSuite | null = null;

  /**
   * 开始测试套件
   */
  startTestSuite(name: string): void {
    this.currentSuite = {
      name,
      tests: [],
      totalDuration: 0,
      successCount: 0,
      failureCount: 0
    };

    console.group(`🧪 开始测试套件: ${name}`);
  }

  /**
   * 结束测试套件
   */
  endTestSuite(): void {
    if (!this.currentSuite) {
      console.warn("❌ 没有活动的测试套件");
      return;
    }

    this.testResults.push(this.currentSuite);

    console.log(`✅ 测试套件完成: ${this.currentSuite.name}`);
    console.log(`📊 总耗时: ${this.currentSuite.totalDuration.toFixed(2)}ms`);
    console.log(`✅ 成功: ${this.currentSuite.successCount}`);
    console.log(`❌ 失败: ${this.currentSuite.failureCount}`);
    console.groupEnd();

    this.currentSuite = null;
  }

  /**
   * 运行单个测试
   */
  async runTest(name: string, testFn: () => Promise<{ duration: number; success: boolean; data?: any }>): Promise<void> {
    if (!this.currentSuite) {
      console.warn("❌ 请先开始测试套件");
      return;
    }

    const startTime = performance.now();
    let result: TestResult;

    try {
      const testResult = await testFn();
      const endTime = performance.now();
      const actualDuration = endTime - startTime;

      result = {
        name,
        duration: testResult.duration || actualDuration,
        success: testResult.success,
        data: testResult.data
      };

      if (result.success) {
        console.log(`✅ ${name}: ${result.duration.toFixed(2)}ms`);
        this.currentSuite.successCount++;
      } else {
        console.warn(`⚠️ ${name}: ${result.duration.toFixed(2)}ms (性能不达标)`);
        this.currentSuite.failureCount++;
      }
    } catch (error) {
      const endTime = performance.now();
      const duration = endTime - startTime;

      result = {
        name,
        duration,
        success: false,
        error: error instanceof Error ? error.message : String(error)
      };

      console.error(`❌ ${name}: ${duration.toFixed(2)}ms - ${result.error}`);
      this.currentSuite.failureCount++;
    }

    this.currentSuite.tests.push(result);
    this.currentSuite.totalDuration += result.duration;
  }

  /**
   * 测试DOM加载性能
   */
  async testDOMPerformance(): Promise<void> {
    await this.runTest("DOM内容加载", async () => {
      const navigation = performance.getEntriesByType("navigation")[0] as PerformanceNavigationTiming;
      const duration = navigation.domContentLoadedEventEnd - navigation.domContentLoadedEventStart;

      return { duration, success: duration < 100 }; // 期望小于100ms
    });

    await this.runTest("DOM完全加载", async () => {
      const navigation = performance.getEntriesByType("navigation")[0] as PerformanceNavigationTiming;
      const duration = navigation.loadEventEnd - navigation.loadEventStart;

      return { duration, success: duration < 200 }; // 期望小于200ms
    });
  }

  /**
   * 测试资源加载性能
   */
  async testResourcePerformance(): Promise<void> {
    await this.runTest("CSS资源加载", async () => {
      const resources = performance.getEntriesByType("resource") as PerformanceResourceTiming[];
      const cssResources = resources.filter(r => r.name.includes(".css"));

      const totalDuration = cssResources.reduce((sum, r) => sum + r.duration, 0);
      const avgDuration = cssResources.length > 0 ? totalDuration / cssResources.length : 0;

      return {
        duration: avgDuration,
        success: avgDuration < 50,
        data: { count: cssResources.length, total: totalDuration }
      };
    });

    await this.runTest("JS资源加载", async () => {
      const resources = performance.getEntriesByType("resource") as PerformanceResourceTiming[];
      const jsResources = resources.filter(r => r.name.includes(".js"));

      const totalDuration = jsResources.reduce((sum, r) => sum + r.duration, 0);
      const avgDuration = jsResources.length > 0 ? totalDuration / jsResources.length : 0;

      return {
        duration: avgDuration,
        success: avgDuration < 100,
        data: { count: jsResources.length, total: totalDuration }
      };
    });
  }

  /**
   * 测试缓存性能
   */
  async testCachePerformance(): Promise<void> {
    await this.runTest("缓存写入性能", async () => {
      const { setStartupCache } = await import("./startupCache");
      const testData = { test: "data", timestamp: Date.now() };

      const startTime = performance.now();
      setStartupCache("menuList", testData);
      const duration = performance.now() - startTime;

      return { duration, success: duration < 10 }; // 期望小于10ms
    });

    await this.runTest("缓存读取性能", async () => {
      const { getStartupCache } = await import("./startupCache");

      const startTime = performance.now();
      const data = getStartupCache("menuList");
      const duration = performance.now() - startTime;

      return { duration, data, success: duration < 5 }; // 期望小于5ms
    });
  }

  /**
   * 测试组件加载性能
   */
  async testComponentPerformance(): Promise<void> {
    await this.runTest("Vue组件创建", async () => {
      const startTime = performance.now();

      // 模拟组件创建
      const { createApp } = await import("vue");
      const app = createApp({
        template: "<div>Test Component</div>"
      });

      const duration = performance.now() - startTime;
      app.unmount();

      return { duration, success: duration < 20 }; // 期望小于20ms
    });
  }

  /**
   * 测试网络性能
   */
  async testNetworkPerformance(): Promise<void> {
    await this.runTest("DNS查询时间", async () => {
      const navigation = performance.getEntriesByType("navigation")[0] as PerformanceNavigationTiming;
      const duration = navigation.domainLookupEnd - navigation.domainLookupStart;

      return { duration, success: duration < 50 }; // 期望小于50ms
    });

    await this.runTest("TCP连接时间", async () => {
      const navigation = performance.getEntriesByType("navigation")[0] as PerformanceNavigationTiming;
      const duration = navigation.connectEnd - navigation.connectStart;

      return { duration, success: duration < 100 }; // 期望小于100ms
    });

    await this.runTest("首字节时间(TTFB)", async () => {
      const navigation = performance.getEntriesByType("navigation")[0] as PerformanceNavigationTiming;
      const duration = navigation.responseStart - navigation.requestStart;

      return { duration, success: duration < 200 }; // 期望小于200ms
    });
  }

  /**
   * 运行完整的性能测试套件
   */
  async runFullTestSuite(): Promise<void> {
    console.log("🚀 开始完整的启动性能测试");

    // DOM性能测试
    this.startTestSuite("DOM性能测试");
    await this.testDOMPerformance();
    this.endTestSuite();

    // 资源加载性能测试
    this.startTestSuite("资源加载性能测试");
    await this.testResourcePerformance();
    this.endTestSuite();

    // 缓存性能测试
    this.startTestSuite("缓存性能测试");
    await this.testCachePerformance();
    this.endTestSuite();

    // 组件性能测试
    this.startTestSuite("组件性能测试");
    await this.testComponentPerformance();
    this.endTestSuite();

    // 网络性能测试
    this.startTestSuite("网络性能测试");
    await this.testNetworkPerformance();
    this.endTestSuite();

    // 生成总结报告
    this.generateSummaryReport();
  }

  /**
   * 生成总结报告
   */
  generateSummaryReport(): void {
    console.group("📊 启动性能测试总结报告");

    let totalTests = 0;
    let totalSuccesses = 0;
    // let totalFailures = 0;
    let totalDuration = 0;

    this.testResults.forEach(suite => {
      totalTests += suite.tests.length;
      totalSuccesses += suite.successCount;
      // totalFailures += suite.failureCount;
      totalDuration += suite.totalDuration;

      console.log(`📋 ${suite.name}:`);
      console.log(`  ✅ 成功: ${suite.successCount}`);
      console.log(`  ❌ 失败: ${suite.failureCount}`);
      console.log(`  ⏱️ 耗时: ${suite.totalDuration.toFixed(2)}ms`);
    });

    console.log("\n🎯 总体统计:");
    console.log(`总测试数: ${totalTests}`);
    console.log(`成功率: ${((totalSuccesses / totalTests) * 100).toFixed(1)}%`);
    console.log(`总耗时: ${totalDuration.toFixed(2)}ms`);

    // 性能建议
    this.generatePerformanceRecommendations();

    console.groupEnd();
  }

  /**
   * 生成性能优化建议
   */
  private generatePerformanceRecommendations(): void {
    console.group("💡 性能优化建议");

    const recommendations: string[] = [];

    this.testResults.forEach(suite => {
      suite.tests.forEach(test => {
        if (!test.success) {
          switch (test.name) {
            case "DOM内容加载":
              if (test.duration > 100) {
                recommendations.push("DOM加载较慢，建议减少同步脚本和优化HTML结构");
              }
              break;
            case "缓存写入性能":
              if (test.duration > 10) {
                recommendations.push("缓存写入较慢，建议检查localStorage性能或减少缓存数据量");
              }
              break;
            case "CSS资源加载":
              if (test.duration > 50) {
                recommendations.push("CSS加载较慢，建议启用压缩和CDN加速");
              }
              break;
            case "JS资源加载":
              if (test.duration > 100) {
                recommendations.push("JS加载较慢，建议代码分割和懒加载");
              }
              break;
          }
        }
      });
    });

    if (recommendations.length === 0) {
      console.log("🎉 所有性能指标都达标！");
    } else {
      recommendations.forEach(rec => console.log(`• ${rec}`));
    }

    console.groupEnd();
  }

  /**
   * 获取测试结果
   */
  getTestResults(): TestSuite[] {
    return [...this.testResults];
  }

  /**
   * 清除测试结果
   */
  clearTestResults(): void {
    this.testResults = [];
    this.currentSuite = null;
  }
}

// 创建全局测试实例
export const startupTester = new StartupPerformanceTester();

// 导出便捷方法
export const runStartupTests = () => startupTester.runFullTestSuite();
export const getStartupTestResults = () => startupTester.getTestResults();
